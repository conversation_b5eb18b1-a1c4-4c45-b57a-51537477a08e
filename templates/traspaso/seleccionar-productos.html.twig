{% extends 'admin/layout.html.twig' %}
{% block titleHead %}{% endblock %}
{% block title %}Transpaso Productos
{% endblock %}
{% block content %}
    <link rel="stylesheet" href="{{ asset('/lib/jQuery-Autocomplete-master/content/styles.css') }}">
    <link rel="stylesheet" href="{{ asset('css/traspaso.css') }}">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.css"/>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

    <input id="url-traspaso-agregar-producto" type="hidden" value="{{ path('traspaso-agregar-producto') }}">
    <input id="url-traspaso-traspasar-productos" type="hidden" value="{{ path('traspaso-traspasar-productos') }}">
    <input id="url-traspaso-add-products" type="hidden" value="{{ path('traspaso-add-products') }}">

    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header card-header-info">
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="card-title">SELECCIONAR PRODUCTOS</h4>
                            </div>
                            <div class="col-md-12 ">
                                <div class="form-group">
                                    <label for="idempresa">Empresa:</label>
                                    <select class="form-control" id="idempresa" onchange="obtenerSucursales()">
                                        <option value="">Seleccione una opción</option>
                                        {% for empresa in empresas %}
                                            <option value="{{ empresa.idempresa }}">{{ empresa.nombre }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>


                            <div id="sucursales"></div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="codigoBarras">Código de barras o SKU</label>
                                    <input type="text" class="form-control" id="codigoBarras" placeholder="">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div>
                                    <label for="codigoBarras">Cargar productos desde Excel</label>
                                    <a
                                            target="_blank"
                                            href="https://docs.google.com/spreadsheets/d/12p_cGGJUnvTamshLPEBlxNLfroCLhS0EL-S0SNzRB9U/edit?usp=sharing"
                                            class="btn btn-link"
                                    >
                                        Plantilla
                                    </a>
                                    <div>
                                        <button id="picker_button" class="btn btn-success d-none"
                                                onclick="createPicker()">Buscar archivo
                                        </button>
                                        <button id="authorize_button" class="btn btn-primary d-none"
                                                onclick="handleAuthClick()">Iniciar sesión
                                        </button>
                                        <button id="signout_button" class="btn btn-danger d-none"
                                                onclick="handleSignoutClick()">Cerrar sesión
                                        </button>
                                        <button
                                                id="error_button"
                                                class="btn btn-warning d-none"
                                                data-toggle="modal"
                                                data-target="#modal-error-detail"
                                                onclick="makeDataTable()"
                                        ></button>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <br>
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="card">
                    <table class="table table-bordered table-borderless">
                        <thead>
                        <tr>
                            <th>Sucursal</th>
                            <th>Código</th>
                            <th>Cantidad</th>
                            <th>Acción</th>                            
                        </tr>
                        </thead>
                        <tbody id="contenedor-productos"></tbody>
                    </table>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="card">
                                <div class="text-center pt-3 pb-3">
                                    <p class="text-sm mb-0 text-capitalize">Productos Agregados</p>
                                    <h1 class="mb-0" id="cantidad-productos"></h1>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 text-center mt-3">
                            <label for="razonTraspaso">¿Por qué se va a traspasar?</label>
                            <textarea id="razonTraspaso" class="form-control border-warning mx-auto my-2"></textarea>
                        </div>
                        <div class="col-md-12 text-center mt-3">
                            <div class="d-flex justify-content-center">

                                <button id="btnTraspasar" class="btn btn-lg btn-info"
                                        onclick="traspasarYDeshabilitar()">Traspasar
                                </button>
                            </div>
                        </div>
                        <div class="col-md-12 text-center">
                            <img id="loader" src="{{ asset('img/log.gif') }}" alt="" width="150px" class="d-none">
                        </div>
                        <div class="col-md-12 text-center" id="contenedor-respuesta"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="mod " id="modal-error-detail" tabindex="-1" aria-labelledby="" aria-hidden="true">
        <div class="mod-dialog modal-dialog-scrollable">
            <div class="modal-content" style="border-radius:10px">
                <div class="modal-header bg-primary">
                    <h1 class="modal-title fs-5">Detalle de errores</h1>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <table class="table" id="error-table">
                        <thead>
                        <th>Código</th>
                        <th>Mensaje</th>
                        </thead>
                        <tbody id="error-table-body">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>

        const CLIENT_ID = '920138527269-euigacmlshkhcm2493hrj2kuebooknro.apps.googleusercontent.com';
        const API_KEY = 'AIzaSyB1-aw7lXPz5acAYPnQF5XjPE66stFeAVk';
        const DISCOVERY_DOC = 'https://sheets.googleapis.com/$discovery/rest?version=v4';
        const SCOPES = 'https://www.googleapis.com/auth/drive.readonly https://www.googleapis.com/auth/spreadsheets.readonly';
        const APP_ID = 'pv360-416621';

        let tokenClient;
        let accessToken = null;
        let pickerInited = false;
        let gisInited = false;
        let gapiInited = false;

        var checkProducts = new Set();
        var errorCount = 0
        var productos;

        $(document).ready(function () {

            $("#error_button").html("<i class='fa-solid fa-triangle-exclamation me-2'></i>" + errorCount);
            $('#idempresa').val(null).trigger('change');
            $("#codigoBarras").on('keyup', function (e) {
                if (e.key === 'Enter' || e.keyCode === 13) {
                    agregarProducto();
                }
            });

            $("#codigoBarras").on('change', function () {
                var codigoBarrasUniversal = $(this).val();
                var url = $("#producto_por_codigo").val();

                $.ajax({
                    url: url,
                    method: 'GET',
                    dataType: "json",
                    data: {
                        codigoBarrasUniversal: codigoBarrasUniversal
                    },
                    success: function (response) {
                        console.log("El ID del producto es: " + response);
                    },
                    error: function () {
                        console.log("Producto no encontrado");
                    }
                });
            });

            $("#codigoBarras").on('change', function () {
                var codigoBarrasUniversal = $(this).val();
                var url1 = $("#orden_salida_aceptar").val();

                var datos = {
                    codigoBarrasUniversal: codigoBarrasUniversal,
                    // Aquí puedes agregar cualquier otro dato que necesites enviar
                    productos: productos,
                    idTranspasoalmacen: idTranspasoalmacen,
                    idSucursalSalida: idSucursalSalida,
                    idSucursalDestino: idSucursalDestino,
                    // etc.
                };

                $.ajax({
                    url: url1,
                    method: 'POST',
                    contentType: "application/json", // Establece el tipo de contenido a JSON
                    data: JSON.stringify(datos), // Convierte los datos a una cadena JSON
                    success: function (response) {
                        console.log("Operación exitosa: " + response);
                    },
                    error: function () {
                        console.log("Error en la operación");
                    }
                });
            });
        });

        function obtenerSucursales() {
            var idempresa = $("#idempresa").val();

            // console.log(url);
            $.ajax({
                url: "{{ path('seleccionar-sucursales') }}",

                data: {
                    idempresa: idempresa
                },
                dataType: "html"
            }).done(function (html) {
                $("#sucursales").html(html);

            }).fail(function () {
                alert("error");
            });
        }

        function agregarProducto() {
            var url = $("#url-traspaso-agregar-producto").val();
            var codigo = $("#codigoBarras").val();
            var codigoExiste = checkProducts.has(codigo);
            var sucursalSalida = $("#sucursalSalida").val();
            // revisamos qe el codigo aún no haya sido ingresado

            if (codigoExiste == false) {
                if (codigo != "") {
                    $.ajax({
                        url: url + "/" + codigo,
                        dataType: "json",
                        method: "POST",
                        data: {
                            sucursalSalida: sucursalSalida
                        },
                        beforeSend: function (xhr) {
                            $("#loader").removeClass("d-none");
                        }
                    }).done(function (response) {
                        console.log(response);
                        if (response.exito) {
                            $("#contenedor-productos").append(response.html);
                            $("#codigoBarras").val("");
                            $("#codigoBarras").focus("");
                            contarProductos();
                            checkProducts.add(codigo)

                        } else {
                            Swal.fire(response.msj, 'Intente Nuevamente', 'warning');
                        }
                        $("#loader").addClass("d-none");

                    });
                }
            } else {
                Swal.fire('El Código ya Existe', 'Ningún producto debe tener el mismo código', 'warning');
                $("#codigoBarras").val("");
                $("#codigoBarras").focus("");
            }

        }

        function addProducts(formatedCodes, quantities = []) {
            $("#error-table").dataTable().fnDestroy();
            $("#error-table-body").html('')
            errorCount = 0
            $("#error_button").addClass("d-none")

            var url = $("#url-traspaso-add-products").val();
            var sucursalSalida = $("#sucursalSalida").val();
            let repeatedCodes = []
            let codes = []
            let codeQuantities = []
            let tempCheckCodes = new Set();

            formatedCodes.forEach((code, index) => {
                let $msg = (checkProducts.has(code)) ? "El producto ya está en la lista" : "El producto está repetido en el archivo"
                if (checkProducts.has(code) || tempCheckCodes.has(code)) repeatedCodes.push({"code": code, "msg": $msg})
                else {
                    codes.push(code)
                    // Get quantity for this code if available, otherwise default to 1
                    codeQuantities.push(quantities[index] || "1")
                    tempCheckCodes.add(code)
                }
            })

            if (codes.length > 0) {

                $.ajax({
                    url: url,
                    dataType: "json",
                    method: "POST",
                    data: {
                        sucursalSalida: sucursalSalida,
                        formatedCodes: codes,
                        quantities: codeQuantities
                    },
                    beforeSend: function (xhr) {
                        $("#loader").removeClass("d-none");
                    }
                }).done(function (response) {
                    console.log(response);
                    if (response.exito) {
                        $("#contenedor-productos").append(response.html);
                        $("#codigoBarras").val("");
                        $("#codigoBarras").focus("");
                        contarProductos();

                        response.codesAdded.forEach((code) => {
                            checkProducts.add(code)
                        })
                    }
                    displayErrorsTable(response.errors)
                    $("#loader").addClass("d-none");
                });

            }

            if (repeatedCodes.length > 0) displayErrorsTable(repeatedCodes)

        }

        function displayErrorsTable(errors) {
            let html = "";
            errorCount += errors.length;
            if (errorCount > 0) {
                $("#error_button").removeClass("d-none");
            }
            errors.forEach((product) => {
                html += "<tr>";
                html += "<td>" + product.code + "</td>";
                html += "<td>" + product.msg;
                // Si existen detalles en el error, se agrega una tabla anidada
                if (product.details) {
                    html += "<br/><table class='table table-bordered mt-2'>";
                    html += "<thead><tr><th>ID Traspaso</th><th>Sucursal Destino</th><th>Cantidad</th></tr></thead>";
                    html += "<tbody>";
                    product.details.forEach((detail) => {
                        html += "<tr><td>" + detail.idtranspasoalmacen + "</td><td>" + detail.sucursalDestino + "</td><td>" + detail.cantidad + "</td></tr>";
                    });
                    html += "</tbody></table>";
                    // Mostrar también la información de stock
                    if (product.stockTotal !== undefined && product.stockPendiente !== undefined && product.stockDisponible !== undefined) {
                        html += "<br/><strong>Stock Total:</strong> " + product.stockTotal +
                            " | <strong>Stock Pendiente:</strong> " + product.stockPendiente +
                            " | <strong>Stock Disponible:</strong> " + product.stockDisponible;
                    }
                }
                html += "</td></tr>";
            });
            $("#error_button").html("<i class='fa-solid fa-triangle-exclamation me-2'></i>" + errorCount);
            $("#error-table-body").append(html);
        }


        function makeDataTable() {
            if (!$.fn.DataTable.isDataTable('#error-table')) {
                $('#error-table').DataTable({
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                        lengthMenu: 'Mostrar _MENU_ códigos por página',
                    },
                    dom: 'Bfrtip',
                    buttons: [
                        {
                            className: 'btn-primary btn',
                            filename: 'codigos_errores',
                            extend: 'excelHtml5',
                            text: 'Exportar excel',
                        }
                    ]
                });
            }
        }

        function contarProductos() {
            var cantidadProductos = 0;
            $(".producto-codigo-cantidad").each(function () {
                cantidadProductos += parseFloat($(this).val());
            });

            $("#cantidad-productos").html(cantidadProductos);

        }

        function traspasarYDeshabilitar() {

            traspasarTodo();

            var btn = document.getElementById('btnTraspasar');
            btn.disabled = true;
            setTimeout(function () {
                btn.disabled = false;
            }, 25000);
        }


        function traspasarTodo() { // Obtener usuario logeado
            var username = "{{ Usuario }}";
            document.getElementById('btnTraspasar').disabled = true;


            // Obtener las sucursales
            var url = $("#url-traspaso-traspasar-productos").val();
            var url1 = $("").val();
            var sucursalSalida = $("#sucursalSalida").val();
            var sucursalDestino = $("#sucursalDestino").val();
            var razonTraspaso = $("#razonTraspaso").val();
            var fechaActual = new Date().toLocaleString(); // fecha y hora actual
            var usuarioActual = username; // usuario actualmente logeado
            var productoid = $("#codigoBarras").val();
            ;
            var numeroProductos = 0;
            var productos = [];

            $(".producto-codigo").each(function () {
                numeroProductos += 1;
                idstock = $(this).data("idstock");
                cantidad = parseFloat($("#cantidad-" + idstock).val());
                // código, idstock y cantidad
                productos.push([$(this).val(), idstock, cantidad]);
            });

            var ordenSalida = {
                sucursalSalida: sucursalSalida,
                sucursalDestino: sucursalDestino,
                fecha: fechaActual,
                usuario: usuarioActual
            };

            console.log(ordenSalida);

            if (sucursalSalida != "") {
                if (sucursalDestino != "") {
                    if (productos.length > 0) {
                        if (razonTraspaso.length > 0) {
                            Swal.fire({
                                title: 'Estas segur@ que deseas traspasar estos productos?',
                                showDenyButton: true,
                                showCancelButton: true,
                                confirmButtonText: 'Si',
                                denyButtonText: `No`
                            }).then((result) => {
                                console.log(result.value);
                                if (result.value) {
                                    $.ajax({
                                        url: url,
                                        dataType: "html",
                                        method: "POST",
                                        data: {
                                            sucursalSalida: sucursalSalida,
                                            sucursalDestino: sucursalDestino,
                                            productos: productos,
                                            razonTraspaso: razonTraspaso,
                                            fecha: fechaActual,
                                            usuario: usuarioActual
                                        },
                                        beforeSend: function (xhr) {
                                            $("#loader").removeClass("d-none");
                                        }
                                    }).done(function (html) {

                                        $("#contenedor-respuesta").html(html);
                                        $("#loader").addClass("d-none");
                                    });
                                }
                            })
                        } else {
                            Swal.fire('Datos Incompletos', 'Debe Agregar un motivo del traspaso', 'warning');
                        }
                    } else {
                        Swal.fire('Datos Incompletos', 'Debe Agregar un Producto', 'warning');
                    }
                } else {
                    Swal.fire('Datos Incompletos', 'Debe Seleccionar una Sucursal de Entrada', 'warning');
                }
            } else {
                Swal.fire('Datos Incompletos', 'Debe Seleccionar una Sucursal de Salida', 'warning');
            }
        }

        function quitarProducto(id, codigo) {
            $("#producto-" + id).remove();
            checkProducts.delete(codigo);
            contarProductos();
        }


        function gapiLoaded() {
            gapi.load('client:picker', initializePicker);
            gapi.load('client', initializeGapiClient);
        }

        async function initializeGapiClient() {
            await gapi.client.init({
                apiKey: API_KEY,
                discoveryDocs: [DISCOVERY_DOC],
            });
            gapiInited = true;
            maybeEnableButtons();
        }

        async function initializePicker() {
            await gapi.client.load('https://www.googleapis.com/discovery/v1/apis/drive/v3/rest');
            pickerInited = true;
            maybeEnableButtons();
        }

        function gisLoaded() {
            tokenClient = google.accounts.oauth2.initTokenClient({
                client_id: CLIENT_ID,
                scope: SCOPES,
                callback: '', // defined later
            });
            gisInited = true;
            maybeEnableButtons();
        }


        function maybeEnableButtons() {
            if (pickerInited && gisInited && gapiInited) {
                $("#authorize_button").removeClass("d-none")
            }
        }


        function handleAuthClick() {
            tokenClient.callback = async (response) => {
                if (response.error !== undefined) {
                    throw (response);
                }
                accessToken = response.access_token;
                $("#signout_button").removeClass("d-none")
                $("#picker_button").removeClass("d-none")
                $("#authorize_button").text("Cambiar de cuenta")

                await createPicker();
            };

            if (accessToken === null) tokenClient.requestAccessToken({prompt: 'consent'});
            else tokenClient.requestAccessToken({prompt: ''});
        }

        function handleSignoutClick() {
            if (accessToken) {
                accessToken = null;
                google.accounts.oauth2.revoke(accessToken);
                $("#authorize_button").text("Iniciar sesión")
                $("#signout_button").addClass("d-none")
                $("#picker_button").addClass("d-none")
                $("#error_button").addClass("d-none")
            }
        }

        function createPicker() {
    try {
        const view = new google.picker.View(google.picker.ViewId.DOCS);
        view.setMimeTypes('application/vnd.google-apps.spreadsheet');
        const picker = new google.picker.PickerBuilder()
            .enableFeature(google.picker.Feature.NAV_HIDDEN)
            .setDeveloperKey(API_KEY)
            .setAppId(APP_ID)
            .setOAuthToken(accessToken)
            .addView(view)
            .addView(new google.picker.DocsUploadView())
            .setCallback(pickerCallback)
            .build();
        picker.setVisible(true);
    } catch (e) {
        console.error("Error en createPicker:", e);
        alert("Error en createPicker: " + e.message);
    }
}

async function pickerCallback(data) {
    try {
        if (data.action === google.picker.Action.PICKED) {
            const document = data[google.picker.Response.DOCUMENTS][0];
            const fileId = document[google.picker.Document.ID];

            // Obtener el nombre real de la primera hoja
            const meta = await gapi.client.sheets.spreadsheets.get({
                spreadsheetId: fileId,
            });
            const sheetName = meta.result.sheets[0].properties.title;

            // Usar el nombre real de la hoja
            const response = await gapi.client.sheets.spreadsheets.values.get({
                spreadsheetId: fileId,
                range: sheetName + '!A:Z',
                majorDimension: 'COLUMNS',
            });

            const values = response.result.values;
            if (values) {
                const formatedCodes = values[0].slice(1).map(value => value.trim());
                // Get quantities from second column if available
                let quantities = [];
                if (values.length > 1) {
                    quantities = values[1].slice(1).map(value => value.trim() || "1"); // Default to 1 if empty
                } else {
                    // If no quantities column, set default quantity of 1 for each code
                    quantities = formatedCodes.map(() => "1");
                }
                addProducts(formatedCodes, quantities);
            }
        }
    } catch (e) {
        console.error("Error en pickerCallback:", e);
        alert("Error en pickerCallback: " + e.message);
    }
}

    </script>
    <script async defer src="https://apis.google.com/js/api.js" onload="gapiLoaded()"></script>
    <script async defer src="https://accounts.google.com/gsi/client" onload="gisLoaded()"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.js"></script>
    <script src="https://cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>


{% endblock %}
