{% if is_supervisor %}
{% extends 'supervisor_layout.html.twig' %}
{% endif %}

{% block titleHead %}{% endblock %}
{% block title %}Reporte de Productos{% endblock %}

{% block stylesheets %}
{{ parent() }}
<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<style>
/* ===== SISTEMA DE DISEÑO CORPORATIVO PROFESIONAL ===== */

:root {
    /* Paleta de colores corporativa refinada */
    --primary-blue: #2563eb;
    --primary-blue-light: #3b82f6;
    --primary-blue-dark: #1d4ed8;
    --secondary-blue: #1e40af;

    --success-green: #059669;
    --success-green-light: #10b981;
    --warning-amber: #d97706;
    --danger-red: #dc2626;

    /* Escala de grises profesional */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --white: #ffffff;

    /* Sistema de sombras corporativo */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Sistema de bordes */
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;

    /* Espaciado consistente */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

/* ===== BASE Y TIPOGRAFÍA CORPORATIVA ===== */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--gray-50);
    color: var(--gray-700);
    line-height: 1.6;
    font-size: 14px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Jerarquía tipográfica profesional */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    letter-spacing: -0.025em;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.dashboard-header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-lg) 0;
    margin-bottom: var(--spacing-xl);
}

.dashboard-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.dashboard-title i {
    color: var(--primary-blue);
    font-size: 1.5rem;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.section-title i {
    color: var(--primary-blue);
    font-size: 1rem;
}

/* ===== SISTEMA DE CARDS CORPORATIVO ===== */
.corporate-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease-in-out;
    overflow: hidden;
}

.corporate-card:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--gray-300);
}

.corporate-card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-100);
    background: var(--gray-50);
}

.corporate-card-body {
    padding: var(--spacing-lg);
}

.corporate-card-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.corporate-card-title i {
    color: var(--primary-blue);
}

/* ===== MÉTRICAS PRINCIPALES CORPORATIVAS ===== */
.metric-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all 0.2s ease-in-out;
    position: relative;
    overflow: hidden;
    height: 100%;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-blue);
}

.metric-card.success::before {
    background: var(--success-green);
}

.metric-card.warning::before {
    background: var(--warning-amber);
}

.metric-card.danger::before {
    background: var(--danger-red);
}

.metric-card:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--gray-300);
}

.metric-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    font-size: 1.25rem;
    color: var(--white);
}

.metric-icon.success {
    background: var(--success-green);
}

.metric-icon.warning {
    background: var(--warning-amber);
}

.metric-icon.primary {
    background: var(--primary-blue);
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
    line-height: 1.2;
}

.metric-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--spacing-xs);
}

.metric-description {
    font-size: 0.75rem;
    color: var(--gray-500);
    line-height: 1.4;
}

/* ===== FORMULARIOS Y CONTROLES CORPORATIVOS ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    display: block;
}

.form-control, .form-select {
    background: var(--white);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    color: var(--gray-700);
    transition: all 0.2s ease-in-out;
    width: 100%;
}

.form-control:focus, .form-select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-control:hover, .form-select:hover {
    border-color: var(--gray-400);
}

.form-control::placeholder {
    color: var(--gray-400);
}

/* Select2 personalizado */
.select2-container--bootstrap-5 .select2-selection {
    border: 1px solid var(--gray-300) !important;
    border-radius: var(--radius-md) !important;
    padding: 0.5rem !important;
    min-height: 44px !important;
    background: var(--white) !important;
}

.select2-container--bootstrap-5 .select2-selection:focus-within {
    border-color: var(--primary-blue) !important;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
}

/* ===== SISTEMA DE BOTONES CORPORATIVO ===== */
.btn-corporate {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius-md);
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    text-decoration: none;
    line-height: 1.4;
}

.btn-corporate:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.btn-primary {
    background: var(--primary-blue);
    color: var(--white);
    border-color: var(--primary-blue);
}

.btn-primary:hover {
    background: var(--primary-blue-dark);
    border-color: var(--primary-blue-dark);
    color: var(--white);
}

.btn-success {
    background: var(--success-green);
    color: var(--white);
    border-color: var(--success-green);
}

.btn-success:hover {
    background: var(--success-green-light);
    border-color: var(--success-green-light);
    color: var(--white);
}

.btn-warning {
    background: var(--warning-amber);
    color: var(--white);
    border-color: var(--warning-amber);
}

.btn-outline-primary {
    background: var(--white);
    color: var(--primary-blue);
    border-color: var(--primary-blue);
}

.btn-outline-primary:hover {
    background: var(--primary-blue);
    color: var(--white);
}

/* ===== CONTENEDORES ESPECIALIZADOS ===== */
.filters-container {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
}

.filters-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.filters-title i {
    color: var(--primary-blue);
}

/* Contenedores de gráficas */
.chart-container {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease-in-out;
    height: 100%;
}

.chart-container:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--gray-300);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--gray-100);
}

.chart-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.chart-title i {
    color: var(--primary-blue);
    font-size: 0.875rem;
}

.chart-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* ===== UTILIDADES Y HELPERS ===== */
.text-muted {
    color: var(--gray-500) !important;
}

.text-primary {
    color: var(--primary-blue) !important;
}

.text-success {
    color: var(--success-green) !important;
}

.text-warning {
    color: var(--warning-amber) !important;
}

.bg-light {
    background-color: var(--gray-50) !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
    .chart-container {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .dashboard-title {
        font-size: 1.5rem;
    }

    .metric-card {
        padding: var(--spacing-lg);
    }

    .metric-value {
        font-size: 1.5rem;
    }

    .corporate-card-body {
        padding: var(--spacing-lg);
    }

    .filters-container {
        padding: var(--spacing-lg);
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
}

@media (max-width: 576px) {
    .dashboard-title {
        font-size: 1.25rem;
    }

    .metric-value {
        font-size: 1.25rem;
    }

    .btn-corporate {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
}

/* ===== ANIMACIONES SUTILES ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.4s ease-out;
}

/* ===== SWEETALERT2 PERSONALIZADO ===== */
.swal2-popup {
    font-family: 'Inter', sans-serif !important;
    border-radius: var(--radius-lg) !important;
    box-shadow: var(--shadow-xl) !important;
    border: 1px solid var(--gray-200) !important;
}

.swal2-title {
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    color: var(--gray-900) !important;
}

.swal2-content {
    font-size: 0.875rem !important;
    color: var(--gray-600) !important;
}

.swal2-confirm {
    background: var(--primary-blue) !important;
    border: none !important;
    border-radius: var(--radius-md) !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
}

.swal2-confirm:hover {
    background: var(--primary-blue-dark) !important;
}

/* ===== ESTILO PARA TÍTULO OPTIMO OPTICAS ===== */
.optimo-title {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-blue);
    text-align: center;
    margin: 2rem 0;
    text-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
    letter-spacing: -0.02em;
}

@media (max-width: 768px) {
    .optimo-title {
        font-size: 2rem;
    }
}

/* ===== EXECUTIVE DASHBOARD DESIGN SYSTEM ===== */

/* Dark Theme Variables */
[data-theme="dark"] {
    --primary-blue: #3b82f6;
    --primary-blue-light: #60a5fa;
    --primary-blue-dark: #2563eb;

    --success-green: #10b981;
    --success-green-light: #34d399;
    --warning-amber: #fbbf24;
    --danger-red: #f87171;

    /* Dark theme grays */
    --gray-50: #0f172a;
    --gray-100: #1e293b;
    --gray-200: #334155;
    --gray-300: #475569;
    --gray-400: #64748b;
    --gray-500: #94a3b8;
    --gray-600: #cbd5e1;
    --gray-700: #e2e8f0;
    --gray-800: #f1f5f9;
    --gray-900: #f8fafc;
    --white: #0f172a;

    /* Dark theme shadows */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* Executive Dashboard Layout */
.executive-dashboard {
    background-color: var(--gray-50);
    min-height: 100vh;
    transition: background-color 0.3s ease;
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    font-size: 1.25rem;
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

/* Hero Metrics Section */
.hero-metrics {
    margin-bottom: 2rem;
}

.hero-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.hero-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.hero-subtitle {
    font-size: 1rem;
    color: var(--gray-600);
    margin: 0.25rem 0 0 0;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

@media (max-width: 768px) {
    .metrics-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Metric Cards */
.metric-card {
    background: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.metric-card.primary {
    border-left: 4px solid var(--primary-blue);
}

.metric-card.success {
    border-left: 4px solid var(--success-green);
}

.metric-card.warning {
    border-left: 4px solid var(--warning-amber);
}

.metric-card.danger {
    border-left: 4px solid var(--danger-red);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.metric-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
}

.metric-icon.primary {
    background: var(--primary-blue);
    color: white !important;
}
.metric-icon.success {
    background: var(--success-green);
    color: white !important;
}
.metric-icon.warning {
    background: var(--warning-amber);
    color: white !important;
}
.metric-icon.danger {
    background: var(--danger-red);
    color: white !important;
}

/* Ensure all metric icons are properly centered */
.metric-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.25rem;
    color: white !important;
    flex-shrink: 0;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
    line-height: 1;
}

.metric-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.metric-description {
    font-size: 0.75rem;
    color: var(--gray-500);
    line-height: 1.4;
}

.metric-trend {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.metric-trend.positive {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-green);
}

.metric-trend.negative {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-red);
}

.metric-trend.neutral {
    background: rgba(148, 163, 184, 0.1);
    color: var(--gray-500);
}

/* Quick Actions Styles */
.quick-actions input:focus,
.quick-actions select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Collapsible Details Styles */
details[open] summary i:last-child {
    transform: rotate(180deg);
}

details summary {
    transition: all 0.3s ease;
}

details summary:hover {
    background: var(--gray-50);
}

[data-theme="dark"] details summary:hover {
    background: var(--gray-200);
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Improvements */
@media (max-width: 640px) {
    .hero-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .theme-toggle {
        top: 10px;
        right: 10px;
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .quick-actions > div {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .detailed-analysis [style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
    }
}

/* Smooth Transitions */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Focus States for Accessibility */
button:focus,
input:focus,
select:focus,
details summary:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .theme-toggle {
        display: none;
    }

    .executive-dashboard {
        background: white !important;
        color: black !important;
    }

    .metric-card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}

/* ===== ADDITIONAL DARK MODE FIXES ===== */

/* Fix any remaining white backgrounds */
[data-theme="dark"] .container-fluid {
    background-color: var(--bg-secondary) !important;
}

[data-theme="dark"] main {
    background-color: var(--bg-secondary) !important;
}

/* Fix Bootstrap form controls */
[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: var(--gray-200) !important;
    border-color: var(--gray-300) !important;
    color: var(--gray-900) !important;
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    background-color: var(--gray-200) !important;
    border-color: var(--primary-blue) !important;
    color: var(--gray-900) !important;
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25) !important;
}

/* Fix checkbox and radio buttons */
[data-theme="dark"] .form-check-input {
    background-color: var(--gray-200) !important;
    border-color: var(--gray-300) !important;
}

[data-theme="dark"] .form-check-input:checked {
    background-color: var(--primary-blue) !important;
    border-color: var(--primary-blue) !important;
}

[data-theme="dark"] .form-check-label {
    color: var(--gray-900) !important;
}

/* Fix any remaining text that might be dark */
[data-theme="dark"] .text-dark {
    color: var(--gray-900) !important;
}

[data-theme="dark"] .text-muted {
    color: var(--gray-600) !important;
}

/* Fix buttons */
[data-theme="dark"] .btn-outline-primary {
    color: var(--primary-blue) !important;
    border-color: var(--primary-blue) !important;
}

[data-theme="dark"] .btn-outline-primary:hover {
    background-color: var(--primary-blue) !important;
    border-color: var(--primary-blue) !important;
    color: white !important;
}

/* Fix any ApexCharts tooltips or legends that might be white */
[data-theme="dark"] .apexcharts-tooltip {
    background: var(--gray-200) !important;
    color: var(--gray-900) !important;
    border-color: var(--gray-300) !important;
}

[data-theme="dark"] .apexcharts-legend-text {
    color: var(--gray-900) !important;
}

/* Fix any remaining white elements */
[data-theme="dark"] * {
    scrollbar-color: var(--gray-400) var(--gray-200);
}

[data-theme="dark"] ::-webkit-scrollbar {
    background-color: var(--gray-200);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background-color: var(--gray-400);
    border-radius: 6px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background-color: var(--gray-500);
}

/* Ensure the body and html have dark background */
[data-theme="dark"] html {
    background-color: var(--bg-secondary) !important;
}

[data-theme="dark"] body {
    background-color: var(--bg-secondary) !important;
}

/* Fix any potential white flashes */
[data-theme="dark"] .executive-dashboard {
    background-color: var(--bg-secondary) !important;
    min-height: 100vh;
}

/* Force dark mode on specific problematic elements */
[data-theme="dark"] .metric-value,
[data-theme="dark"] .metric-label,
[data-theme="dark"] .metric-description {
    color: var(--gray-900) !important;
}

[data-theme="dark"] .hero-title,
[data-theme="dark"] .hero-subtitle {
    color: var(--gray-900) !important;
}

/* Fix any remaining Bootstrap components */
[data-theme="dark"] .alert {
    background-color: var(--gray-200) !important;
    border-color: var(--gray-300) !important;
    color: var(--gray-900) !important;
}

[data-theme="dark"] .modal-content {
    background-color: var(--bg-primary) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .modal-header {
    border-bottom-color: var(--border-primary) !important;
}

[data-theme="dark"] .modal-footer {
    border-top-color: var(--border-primary) !important;
}

/* Fix dropdown menus */
[data-theme="dark"] .dropdown-menu {
    background-color: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;
}

[data-theme="dark"] .dropdown-item {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .dropdown-item:hover {
    background-color: var(--gray-200) !important;
    color: var(--gray-900) !important;
}

/* Fix any table elements */
[data-theme="dark"] .table {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: var(--gray-200) !important;
}

/* Fix loading spinners and icons */
[data-theme="dark"] .fa-spinner,
[data-theme="dark"] .fas,
[data-theme="dark"] .far,
[data-theme="dark"] .fab {
    color: inherit !important;
}

/* ===== METRIC CARDS DARK MODE FIXES ===== */

/* Ensure metric card colors are preserved in dark mode */
[data-theme="dark"] .metric-card.primary {
    border-left-color: var(--primary-blue) !important;
}

[data-theme="dark"] .metric-card.success {
    border-left-color: var(--success-green) !important;
}

[data-theme="dark"] .metric-card.warning {
    border-left-color: var(--warning-amber) !important;
}

[data-theme="dark"] .metric-card.danger {
    border-left-color: var(--danger-red) !important;
}

/* Metric icons - preserve colors and center them */
[data-theme="dark"] .metric-icon.primary {
    background-color: var(--primary-blue) !important;
    color: white !important;
}

[data-theme="dark"] .metric-icon.success {
    background-color: var(--success-green) !important;
    color: white !important;
}

[data-theme="dark"] .metric-icon.warning {
    background-color: var(--warning-amber) !important;
    color: white !important;
}

[data-theme="dark"] .metric-icon.danger {
    background-color: var(--danger-red) !important;
    color: white !important;
}

/* Center icons in metric cards */
.metric-icon {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Metric trend colors in dark mode */
[data-theme="dark"] .metric-trend.positive {
    background-color: rgba(16, 185, 129, 0.2) !important;
    color: var(--success-green) !important;
}

[data-theme="dark"] .metric-trend.negative {
    background-color: rgba(239, 68, 68, 0.2) !important;
    color: var(--danger-red) !important;
}

[data-theme="dark"] .metric-trend.neutral {
    background-color: rgba(148, 163, 184, 0.2) !important;
    color: var(--gray-500) !important;
}

/* Ensure trend icons are visible */
[data-theme="dark"] .metric-trend i {
    color: inherit !important;
}

/* Ensure proper inheritance for all text elements */
[data-theme="dark"] h1, [data-theme="dark"] h2, [data-theme="dark"] h3,
[data-theme="dark"] h4, [data-theme="dark"] h5, [data-theme="dark"] h6,
[data-theme="dark"] p, [data-theme="dark"] span, [data-theme="dark"] div,
[data-theme="dark"] label, [data-theme="dark"] small {
    color: inherit !important;
}

/* Fix any remaining white borders or backgrounds */
[data-theme="dark"] * {
    border-color: var(--border-primary);
}

[data-theme="dark"] *:not(.metric-card):not(.btn):not(.form-control):not(.form-select) {
    background-color: inherit;
}

/* ===== SPECIFIC FIXES FOR CHART TITLES AND SECTION TEXTS ===== */

/* Force white text for all titles within analysis sections */
[data-theme="dark"] .analysis-section h4,
[data-theme="dark"] .analysis-section h3,
[data-theme="dark"] .analysis-section h2,
[data-theme="dark"] .analysis-section h1 {
    color: var(--text-primary) !important;
}

/* Force white text for all labels within analysis sections */
[data-theme="dark"] .analysis-section label,
[data-theme="dark"] .analysis-section span,
[data-theme="dark"] .analysis-section p,
[data-theme="dark"] .analysis-section div {
    color: var(--text-primary) !important;
}

/* Specific fixes for chart container titles */
[data-theme="dark"] .detailed-analysis h4,
[data-theme="dark"] .advanced-analysis h4,
[data-theme="dark"] .products-analysis h4,
[data-theme="dark"] .billing-analysis h4,
[data-theme="dark"] .advanced-config h4 {
    color: var(--text-primary) !important;
}

/* Force white text for summary titles */
[data-theme="dark"] details summary {
    color: var(--text-primary) !important;
}

/* Force white text for all text within the executive dashboard */
[data-theme="dark"] .executive-dashboard h1,
[data-theme="dark"] .executive-dashboard h2,
[data-theme="dark"] .executive-dashboard h3,
[data-theme="dark"] .executive-dashboard h4,
[data-theme="dark"] .executive-dashboard h5,
[data-theme="dark"] .executive-dashboard h6 {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .executive-dashboard label,
[data-theme="dark"] .executive-dashboard span:not(.metric-value):not(.metric-label),
[data-theme="dark"] .executive-dashboard p,
[data-theme="dark"] .executive-dashboard div:not(.metric-card) {
    color: var(--text-primary) !important;
}

/* Specific fix for inline styles that might override */
[data-theme="dark"] [style*="color: var(--gray-900)"] {
    color: var(--text-primary) !important;
}

/* Force white text for any element with dark gray color */
[data-theme="dark"] [style*="color: #0f172a"],
[data-theme="dark"] [style*="color: #1e293b"],
[data-theme="dark"] [style*="color: #334155"] {
    color: var(--text-primary) !important;
}

/* Fix for elements that might have inherited dark colors */
[data-theme="dark"] .executive-dashboard * {
    color: inherit;
}

/* Override any remaining dark text */
[data-theme="dark"] .executive-dashboard {
    color: var(--text-primary);
}

/* Ensure chart titles are white */
[data-theme="dark"] .chart-title,
[data-theme="dark"] .chart-header h3,
[data-theme="dark"] .chart-header h4 {
    color: var(--text-primary) !important;
}

/* Fix for any ApexCharts text elements */
[data-theme="dark"] .apexcharts-text {
    fill: var(--text-primary) !important;
}

[data-theme="dark"] .apexcharts-title-text {
    fill: var(--text-primary) !important;
}

[data-theme="dark"] .apexcharts-subtitle-text {
    fill: var(--text-primary) !important;
}
</style>
{% endblock %}

{% block content %}
<!-- Theme Toggle Button -->
<button class="theme-toggle" onclick="toggleTheme()" title="Cambiar tema">
  <span id="theme-icon">🌙</span>
</button>

<!-- Executive Dashboard -->
<div class="executive-dashboard" data-theme="light">

  <!-- Hero Section - Métricas Principales -->
  <section class="hero-metrics">
    <div class="hero-header">
      <div>
        <h1 class="hero-title">
          <i class="fas fa-tachometer-alt" style="color: var(--primary-blue); margin-right: 0.5rem;"></i>
          {% if is_supervisor %}Dashboard Supervisor{% else %}Dashboard Administrador{% endif %}
        </h1>
        <p class="hero-subtitle">
          <i class="fas fa-building" style="margin-right: 0.25rem;"></i>
          Optimo Opticas - Resumen Ejecutivo
        </p>
      </div>
      <div style="text-align: right; color: var(--gray-600); font-size: 0.875rem;">
        <div><i class="fas fa-calendar"></i> <span id="current-date"></span></div>
        <div><i class="fas fa-clock"></i> <span id="current-time"></span></div>
      </div>
    </div>

    <!-- Métricas Principales -->
    <div class="metrics-grid">
      <div class="metric-card success">
        <div class="metric-header">
          <div class="metric-icon success">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="metric-trend positive" id="ventas-trend">
            <i class="fas fa-arrow-up"></i> +12.5%
          </div>
        </div>
        <div class="metric-value" id="ventas-total">$0.00</div>
        <div class="metric-label">Ventas del Día</div>
        <div class="metric-description">Total de ventas registradas hoy</div>
      </div>

      <div class="metric-card primary">
        <div class="metric-header">
          <div class="metric-icon primary">
            <i class="fas fa-money-bill-wave"></i>
          </div>
          <div class="metric-trend positive" id="pagos-trend">
            <i class="fas fa-arrow-up"></i> +8.3%
          </div>
        </div>
        <div class="metric-value" id="pagos-total">$0.00</div>
        <div class="metric-label">Pagos Recibidos</div>
        <div class="metric-description">Total de pagos cobrados hoy</div>
      </div>

      <div class="metric-card warning">
        <div class="metric-header">
          <div class="metric-icon warning">
            <i class="fas fa-clock"></i>
          </div>
          <div class="metric-trend neutral" id="deuda-trend">
            <i class="fas fa-minus"></i> 0%
          </div>
        </div>
        <div class="metric-value" id="por-cobrar-total">$0.00</div>
        <div class="metric-label">Por Cobrar</div>
        <div class="metric-description">Pendiente de cobro</div>
      </div>
    </div>
  </section>

  <!-- Quick Actions Section -->
  <section class="quick-actions" style="margin-bottom: 2rem;">
    <div style="background: var(--white); border-radius: 12px; padding: 1.5rem; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200);">
      <h3 style="font-size: 1.125rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
        <i class="fas fa-sliders-h" style="color: var(--primary-blue);"></i>
        Acciones Rápidas
      </h3>

      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
        <div>
          <label for="fecha-hoy" style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
            <i class="fas fa-calendar"></i> Fecha:
          </label>
          <input type="date" id="fecha-hoy" style="width: 100%; padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--white); color: var(--gray-900);">
        </div>

        <div>
          <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
            <i class="fas fa-store"></i> Sucursales:
          </label>
          <div id="sucursales-summary" style="padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--gray-50); color: var(--gray-700); cursor: pointer;" onclick="toggleSucursalesPanel()">
            <span id="sucursales-count">Cargando...</span>
            <i class="fas fa-chevron-down" style="float: right; margin-top: 0.125rem;"></i>
          </div>
        </div>

        <div>
          <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
            <i class="fas fa-building"></i> Empresa:
          </label>
          {% if not is_hardcoded %}
            <select name="empresa" id="idempresa" style="width: 100%; padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--white); color: var(--gray-900);" onchange="cargaDefiltros();">
              <option value="-1">Seleccione una empresa</option>
              {% for empresa in empresas %}
                <option value="{{ empresa.idempresa }}">{{ empresa.nombre }}</option>
              {% endfor %}
            </select>
          {% else %}
            <input type="hidden" id="idempresa" value="{{ empresas[0].idempresa }}">
            <div style="padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--gray-50); color: var(--gray-700);">
              <i class="fas fa-lock" style="margin-right: 0.5rem;"></i>
              {{ empresas[0].nombre }} (BIMBO)
            </div>
          {% endif %}
        </div>

        <div>
          <button onclick="buscarIngresosDiarios()" style="width: 100%; padding: 0.75rem 1rem; background: var(--primary-blue); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer; transition: all 0.2s ease; display: flex; align-items: center; justify-content: center; gap: 0.5rem;" onmouseover="this.style.background='var(--primary-blue-dark)'" onmouseout="this.style.background='var(--primary-blue)'">
            <i class="fas fa-sync-alt"></i>
            Actualizar
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Sucursales Panel (Collapsible) -->
  <div id="sucursales-panel" style="display: none; margin-bottom: 2rem;">
    <div style="background: var(--white); border-radius: 12px; padding: 1.5rem; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200);">
      <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0;">
        <i class="fas fa-store"></i> Seleccionar Sucursales
      </h4>
      <div id="sucursales"></div>
    </div>
  </div>

  <!-- Hidden inputs for compatibility -->
  <input type="hidden" id="tipo-venta" value="BIMBO">

  <!-- Detailed Analysis Section (Open by default) -->
  <section class="detailed-analysis">
    <details class="analysis-section" open style="background: var(--white); border-radius: 12px; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200); margin-bottom: 2rem;">
      <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
        <i class="fas fa-chart-pie" style="color: var(--primary-blue);"></i>
        📊 Análisis de Ingresos Diarios
        <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
      </summary>

      <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--gray-200);">
        <!-- Charts Grid -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem; margin-top: 1.5rem;">

          <!-- Ingresos por Sucursal -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-store" style="color: var(--success-green);"></i>
              Ingresos por Sucursal
            </h4>
            <div id="graficaSucursal" style="height: 300px; min-height: 300px;"></div>
          </div>

          <!-- Tipos de Pago -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-credit-card" style="color: var(--primary-blue);"></i>
              Tipos de Pago
            </h4>
            <div id="graficaTipoPago" style="height: 300px; min-height: 300px;"></div>
          </div>

          <!-- Deuda por Sucursal -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200); grid-column: 1 / -1;">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-exclamation-triangle" style="color: var(--warning-amber);"></i>
              Deuda por Sucursal
            </h4>
            <div id="deudaTotal" style="height: 300px; min-height: 300px; display: flex; align-items: center; justify-content: center; color: var(--gray-500);">
              <div style="text-align: center;">
                <i class="fas fa-spinner fa-spin fa-2x" style="margin-bottom: 1rem;"></i>
                <p>Cargando datos...</p>
              </div>
            </div>
          </div>

        </div>
      </div>
    </details>
  </section>

  <!-- Análisis Anual Section (Open by default) -->
  <section class="advanced-analysis">
    <details class="analysis-section" open style="background: var(--white); border-radius: 12px; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200); margin-bottom: 2rem;">
      <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
        <i class="fas fa-chart-bar" style="color: var(--success-green);"></i>
        📈 Análisis Anual
        <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
      </summary>

      <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--gray-200);">
        <!-- Year Selector -->
        <div style="margin-bottom: 1.5rem;">
          <label for="year-select" style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
            <i class="fas fa-calendar"></i> Seleccionar Año:
          </label>
          <select id="year-select" name="year" style="padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--white); color: var(--gray-900); min-width: 150px;">
            <option value="2024" selected>2024</option>
            <option value="2023">2023</option>
            <option value="2022">2022</option>
          </select>
          <button onclick="buscarVentasAnuales()" style="margin-left: 1rem; padding: 0.75rem 1rem; background: var(--success-green); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer;">
            <i class="fas fa-sync-alt"></i> Actualizar
          </button>
        </div>

        <!-- Annual Charts Grid -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 1.5rem;">

          <!-- Ventas Mensuales -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-chart-line" style="color: var(--success-green);"></i>
              Ventas Mensuales
            </h4>
            <div id="sumaMontos" style="height: 350px; min-height: 350px;"></div>
          </div>

          <!-- Pagos por Sucursal -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-chart-pie" style="color: var(--primary-blue);"></i>
              Pagos por Sucursal
            </h4>
            <div id="sumaPagos" style="height: 350px; min-height: 350px;"></div>
          </div>

        </div>
      </div>
    </details>
  </section>

  <!-- Productos y Marcas Section (Open by default) -->
  <section class="products-analysis">
    <details class="analysis-section" open style="background: var(--white); border-radius: 12px; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200); margin-bottom: 2rem;">
      <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
        <i class="fas fa-tags" style="color: var(--warning-amber);"></i>
        🏷️ Productos y Marcas
        <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
      </summary>

      <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--gray-200);">
        <!-- Year Selector for Products -->
        <div style="margin-bottom: 1.5rem;">
          <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
            <i class="fas fa-calendar"></i> Año para Análisis de Productos:
          </label>
          <select id="year-select-productos" style="padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--white); color: var(--gray-900); min-width: 150px;">
            <option value="2024" selected>2024</option>
            <option value="2023">2023</option>
            <option value="2022">2022</option>
          </select>
          <button onclick="buscarMarcasYModelos()" style="margin-left: 1rem; padding: 0.75rem 1rem; background: var(--warning-amber); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer;">
            <i class="fas fa-sync-alt"></i> Actualizar
          </button>
        </div>

        <!-- Products Charts Grid -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 1.5rem;">

          <!-- Análisis de Marcas -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-tag" style="color: var(--warning-amber);"></i>
              Modelos por Marca
            </h4>
            <div id="recuentoMarcas" style="height: 350px; min-height: 350px;"></div>
          </div>

          <!-- Modelos y Tratamientos -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-cogs" style="color: var(--info);"></i>
              Distribución de Tratamientos
            </h4>
            <div id="tratamientoGrafica" style="height: 350px; min-height: 350px;"></div>
          </div>

        </div>
      </div>
    </details>
  </section>

  <!-- Facturación Section (Open by default) -->
  <section class="billing-analysis">
    <details class="analysis-section" open style="background: var(--white); border-radius: 12px; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200); margin-bottom: 2rem;">
      <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
        <i class="fas fa-file-invoice-dollar" style="color: var(--info);"></i>
        🧾 Análisis de Facturación
        <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
      </summary>

      <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--gray-200);">
        <!-- Year Selector for Billing -->
        <div style="margin-bottom: 1.5rem;">
          <label for="year-select-facturacion" style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
            <i class="fas fa-calendar"></i> Año para Facturación:
          </label>
          <select id="year-select-facturacion" name="year" style="padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--white); color: var(--gray-900); min-width: 150px;">
            <option value="2024" selected>2024</option>
            <option value="2023">2023</option>
            <option value="2022">2022</option>
          </select>
          <button onclick="buscarDatosFacturacion()" style="margin-left: 1rem; padding: 0.75rem 1rem; background: var(--info); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer;">
            <i class="fas fa-sync-alt"></i> Actualizar
          </button>
        </div>

        <!-- Billing Charts Grid -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem;">

          <!-- Facturación por Estado -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-info-circle" style="color: var(--info);"></i>
              Facturación por Estado
            </h4>
            <div id="estatus" style="height: 300px; min-height: 300px;"></div>
          </div>

          <!-- Total Facturado -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-dollar-sign" style="color: var(--success-green);"></i>
              Total Facturado
            </h4>
            <div id="tio" style="height: 300px; min-height: 300px;"></div>
          </div>

          <!-- Facturación Mensual -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200); grid-column: 1 / -1;">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-chart-line" style="color: var(--primary-blue);"></i>
              Evolución Mensual de Facturación
            </h4>
            <div id="Sumaim" style="height: 350px; min-height: 350px;"></div>
          </div>

        </div>
      </div>
    </details>
  </section>

  <!-- Configuración Avanzada Section (Closed by default) -->
  <section class="advanced-config">
    <details class="analysis-section" style="background: var(--white); border-radius: 12px; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200); margin-bottom: 2rem;">
      <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
        <i class="fas fa-cogs" style="color: var(--gray-600);"></i>
        ⚙️ Configuración Avanzada
        <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
      </summary>

      <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--gray-200);">
        <!-- Advanced Date Range -->
        <div style="margin-bottom: 1.5rem;">
          <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0;">
            <i class="fas fa-calendar-alt"></i> Rango de Fechas Personalizado
          </h4>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
            <div>
              <label for="fecha-inicio-rango-dia" style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                Fecha Inicio:
              </label>
              <input id="fecha-inicio-rango-dia" type="date" style="width: 100%; padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--white); color: var(--gray-900);">
            </div>
            <div>
              <label for="fecha-fin-rango-dia" style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                Fecha Fin:
              </label>
              <input id="fecha-fin-rango-dia" type="date" style="width: 100%; padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--white); color: var(--gray-900);">
            </div>
            <div>
              <button onclick="resetRangoFechaDias()" style="width: 100%; padding: 0.75rem; background: var(--gray-500); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer;">
                <i class="fas fa-eraser"></i> Limpiar
              </button>
            </div>
            <div>
              <button onclick="actualizarTodasLasGraficas()" style="width: 100%; padding: 0.75rem; background: var(--primary-blue); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer;">
                <i class="fas fa-search"></i> Buscar
              </button>
            </div>
          </div>
        </div>
      </div>
    </details>
  </section>

</div>
<!-- End Executive Dashboard -->

<!-- Hidden Elements for Compatibility -->
<div style="display: none;">
  <!-- Keep all the old content hidden for JavaScript compatibility -->
      <div class="filters-container">
        <h3 class="filters-title">
          <i class="fas fa-calendar"></i>
          Ventas Acumuladas por Año
        </h3>
        <div class="row g-3 align-items-end">
          <div class="col-md-3">
            <label for="year-select" class="form-label">Año:</label>
            <select name="year" id="year-select" class="form-select">
              <!-- Options will be dynamically populated here -->
            </select>
          </div>
        </div>
      </div>

      <div class="chart-container mb-4">
        <div class="chart-header">
          <h3 class="chart-title">
            <i class="fas fa-chart-bar"></i>
            Análisis de Ventas Anuales
          </h3>
          <div class="chart-actions">
            <button class="btn btn-success btn-corporate btn-sm" onclick="buscarVentasAnuales()">
              <i class="fas fa-chart-bar"></i> Actualizar Ventas
            </button>
          </div>
        </div>
        <!-- Sección duplicada eliminada - ahora solo usamos la del dashboard ejecutivo -->
      </div>
      <!-- Sección de Marcas y Modelos -->
      <div class="chart-container mb-4">
        <div class="chart-header">
          <h3 class="chart-title">
            <i class="fas fa-tags"></i>
            Análisis de Marcas y Modelos
          </h3>
          <div class="chart-actions">
            <button class="btn btn-outline-primary btn-corporate btn-sm" onclick="buscarVentasAnuales()">
              <i class="fas fa-sync-alt"></i> Actualizar Marcas
            </button>
          </div>
        </div>
        <div class="row g-4">
          <div class="col-lg-6">
            <div class="corporate-card">
              <div class="corporate-card-header">
                <h4 class="corporate-card-title">
                  <i class="fas fa-trademark"></i>
                  Modelos por Marca
                </h4>
              </div>
              <div class="corporate-card-body">
                <div id="recuentoMarcas" style="height: 400px; min-height: 400px;"></div>
              </div>
            </div>
          </div>
          <div class="col-lg-6">
            <div class="corporate-card">
              <div class="corporate-card-header">
                <h4 class="corporate-card-title">
                  <i class="fas fa-chart-pie"></i>
                  Distribución de Tratamientos
                </h4>
              </div>
              <div class="corporate-card-body">
                <div id="tratamientoGrafica" style="height: 400px; min-height: 400px;"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sección de Facturación -->
      <div class="filters-container">
        <h3 class="filters-title">
          <i class="fas fa-file-invoice"></i>
          Configuración de Facturación
        </h3>
        <div class="row g-3 align-items-end">
          <div class="col-md-3">
            <label for="year-select-facturacion" class="form-label">Año:</label>
            <select name="year" id="year-select-facturacion" class="form-select">
              <!-- Options will be dynamically populated here -->
            </select>
          </div>
        </div>
      </div>

      <!-- Métrica de Suma de Importes -->
      <div class="row g-4 mb-4">
        <div class="col-12">
          <div class="metric-card success">
            <div class="metric-icon success">
              <i class="fas fa-file-invoice-dollar"></i>
            </div>
            <div class="metric-value" id="suma-im">$0.00</div>
            <div class="metric-label">Total Facturado</div>
            <div class="metric-description">Suma total de importes facturados</div>
          </div>
        </div>
      </div>
      <!-- Sección duplicada eliminada - ahora solo usamos la sección nueva en el dashboard ejecutivo -->
    </div>
  </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script src="{{ asset('lib/apexcharts-bundle/dist/apexcharts.min.js') }}"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/es.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.2/js/dataTables.buttons.min.js"></script>

<script>
// ===== FUNCIONES PRINCIPALES DE CARGA =====
function actualizarTodasLasGraficas() {
     ('Actualizando todas las gráficas del dashboard...');
    mostrarBarraProgreso();
    cargarGraficasSecuencialmente();
}

// ===== SISTEMA DE CARGA SECUENCIAL =====
async function cargarGraficasSecuencialmente() {
    const pasos = [
        { nombre: 'Resumen de Ingresos', funcion: cargarResumenIngresos, progreso: 20 },
        { nombre: 'Gráficas de Ingresos Diarios', funcion: cargarGraficasIngresosDiarios, progreso: 40 },
        { nombre: 'Gráficas de Ventas Anuales', funcion: cargarGraficasVentasAnuales, progreso: 60 },
        { nombre: 'Gráficas de Marcas y Modelos', funcion: cargarGraficasMarcasYModelos, progreso: 80 },
        { nombre: 'Gráficas de Facturación', funcion: cargarGraficasFacturacion, progreso: 100 }
    ];

    for (let i = 0; i < pasos.length; i++) {
        const paso = pasos[i];
        actualizarProgreso(paso.progreso, `Cargando ${paso.nombre}...`);

        try {
            await new Promise((resolve) => {
                setTimeout(() => {
                    paso.funcion();
                    resolve();
                }, 500); // Pequeña pausa entre cargas para mejor UX
            });
        } catch (error) {
            console.error(`Error al cargar ${paso.nombre}:`, error);
        }
    }

    setTimeout(() => {
        ocultarBarraProgreso();
        mostrarMensajeExito('¡Dashboard actualizado correctamente!');
    }, 1000);
}

// Función para cuando cambia la empresa
function onEmpresaChange() {
    actualizarTodasLasGraficas();
}

// ===== FUNCIONES ESPECÍFICAS PARA CADA BOTÓN =====
function buscarIngresosDiarios() {
     ('Buscando ingresos diarios...');

    // Validar que se hayan seleccionado fechas y sucursales
    const fechaHoy = document.getElementById('fecha-hoy').value;
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')];

    if (!fechaHoy) {
        mostrarAlertaAdvertencia('Por favor, seleccione una fecha.');
        return;
    }

    if (sucursales.length === 0) {
        mostrarAlertaAdvertencia('Por favor, seleccione al menos una sucursal.');
        return;
    }

    // Cargar datos
    cargarResumenIngresos();
    cargarGraficasIngresosDiarios(false); // isAutoLoad = false (llamada manual)
    mostrarMensajeExito('Ingresos diarios actualizados correctamente');
}

function buscarVentasAnuales() {
     ('Buscando ventas anuales...');

    // Validar que se haya seleccionado año y sucursales
    const year = document.getElementById('year-select').value;
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')];

    if (!year) {
        mostrarAlertaAdvertencia('Por favor, seleccione un año.');
        return;
    }

    if (sucursales.length === 0) {
        mostrarAlertaAdvertencia('Por favor, seleccione al menos una sucursal.');
        return;
    }

    // Cargar datos
    cargarGraficasVentasAnuales();
    cargarGraficasMarcasYModelos();
    mostrarMensajeExito('Ventas anuales actualizadas correctamente');
}

function buscarDatosFacturacion() {
    console.log('🔄 Iniciando búsqueda de datos de facturación...');

    // Validar que se haya seleccionado año
    const year = document.getElementById('year-select-facturacion').value || new Date().getFullYear();
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')].map(cb => cb.value).join(',');

    console.log('📅 Año seleccionado:', year);
    console.log('🏪 Sucursales seleccionadas:', sucursales);

    if (!sucursales) {
        mostrarAlertaAdvertencia('Por favor, seleccione al menos una sucursal.');
        return;
    }

    // Mostrar indicadores de carga mejorados
    const estatusElement = document.getElementById('estatus');
    const tioElement = document.getElementById('tio');
    const sumaimElement = document.getElementById('Sumaim');

    const loadingHTML = `
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--gray-500);">
            <div style="text-align: center;">
                <i class="fas fa-spinner fa-spin fa-2x" style="margin-bottom: 1rem; color: var(--primary-blue);"></i>
                <p>Cargando datos de facturación...</p>
                <small style="color: var(--gray-400);">Esto puede tomar unos segundos</small>
            </div>
        </div>
    `;

    if (estatusElement) estatusElement.innerHTML = loadingHTML;
    if (tioElement) tioElement.innerHTML = loadingHTML;
    if (sumaimElement) sumaimElement.innerHTML = loadingHTML;

    // Hacer llamada a la API
    const startTime = performance.now();

    fetch(`/cliente-api/get-facturacion-data?year=${year}&sucursales=${sucursales}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            const endTime = performance.now();
            console.log(`✅ Datos de facturación recibidos en ${(endTime - startTime).toFixed(2)}ms:`, data);

            // Verificar si hay datos
            const hasData = (
                (data.facturasPorEstado && data.facturasPorEstado.length > 0) ||
                (data.facturacionPorSucursal && data.facturacionPorSucursal.length > 0) ||
                (data.facturacionMensual && data.facturacionMensual.length > 0)
            );

            if (!hasData) {
                console.log('⚠️ No se encontraron datos de facturación');
                mostrarMensajeNoData();
                return;
            }

            // Cargar gráficas con datos reales
            cargarGraficasFacturacionConDatos(data);
            mostrarMensajeExito('Datos de facturación actualizados correctamente');

        })
        .catch(error => {
            console.error('❌ Error al cargar datos de facturación:', error);
            mostrarAlertaError('Error al cargar los datos de facturación. Por favor, intente de nuevo.');
            mostrarMensajeError();
        });
}

// Función para mostrar mensaje cuando no hay datos
function mostrarMensajeNoData() {
    const noDataHTML = `
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--gray-500);">
            <div style="text-align: center;">
                <i class="fas fa-inbox fa-3x" style="margin-bottom: 1rem; color: var(--gray-400);"></i>
                <h4 style="color: var(--gray-600); margin-bottom: 0.5rem;">No hay datos de facturación</h4>
                <p style="color: var(--gray-500); margin: 0;">
                    Aún no se han registrado facturas para el período seleccionado.
                </p>
                <small style="color: var(--gray-400); margin-top: 0.5rem; display: block;">
                    Intente seleccionar un año diferente o verifique que haya ventas facturadas.
                </small>
            </div>
        </div>
    `;

    const estatusElement = document.getElementById('estatus');
    const tioElement = document.getElementById('tio');
    const sumaimElement = document.getElementById('Sumaim');

    if (estatusElement) estatusElement.innerHTML = noDataHTML;
    if (tioElement) tioElement.innerHTML = noDataHTML;
    if (sumaimElement) sumaimElement.innerHTML = noDataHTML;
}

// Función para mostrar mensaje de error
function mostrarMensajeError() {
    const errorHTML = `
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--danger-red);">
            <div style="text-align: center;">
                <i class="fas fa-exclamation-triangle fa-3x" style="margin-bottom: 1rem;"></i>
                <h4 style="color: var(--danger-red); margin-bottom: 0.5rem;">Error al cargar datos</h4>
                <p style="color: var(--gray-500); margin: 0;">
                    Hubo un problema al obtener los datos de facturación.
                </p>
                <button onclick="buscarDatosFacturacion()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: var(--primary-blue); color: white; border: none; border-radius: 6px; cursor: pointer;">
                    <i class="fas fa-redo"></i> Reintentar
                </button>
            </div>
        </div>
    `;

    const estatusElement = document.getElementById('estatus');
    const tioElement = document.getElementById('tio');
    const sumaimElement = document.getElementById('Sumaim');

    if (estatusElement) estatusElement.innerHTML = errorHTML;
    if (tioElement) tioElement.innerHTML = errorHTML;
    if (sumaimElement) sumaimElement.innerHTML = errorHTML;
}

// Función para cargar gráficas de facturación con datos reales
function cargarGraficasFacturacionConDatos(data) {
    console.log('📊 Cargando gráficas de facturación con datos:', data);

    // 1. Cargar gráfica de facturas por estado
    if (data.facturasPorEstado && data.facturasPorEstado.length > 0) {
        console.log('✅ Cargando facturas por estado...');
        cargarGraficaFacturasPorEstado(data.facturasPorEstado);
    } else {
        console.log('⚠️ No hay datos de facturas por estado');
        document.getElementById('estatus').innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--gray-500);">
                <div style="text-align: center;">
                    <i class="fas fa-info-circle fa-2x" style="margin-bottom: 1rem; color: var(--gray-400);"></i>
                    <p>No hay facturas por estado</p>
                </div>
            </div>
        `;
    }

    // 2. Cargar gráfica de total facturado
    if (data.facturacionPorSucursal && data.facturacionPorSucursal.length > 0) {
        console.log('✅ Cargando total facturado...');
        cargarGraficaTotalFacturado(data.facturacionPorSucursal);
    } else {
        console.log('⚠️ No hay datos de facturación por sucursal');
        document.getElementById('tio').innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--gray-500);">
                <div style="text-align: center;">
                    <i class="fas fa-info-circle fa-2x" style="margin-bottom: 1rem; color: var(--gray-400);"></i>
                    <p>No hay datos de facturación por sucursal</p>
                </div>
            </div>
        `;
    }

    // 3. Cargar gráfica de facturación mensual
    if (data.facturacionMensual && data.facturacionMensual.length > 0) {
        console.log('✅ Cargando facturación mensual...');
        cargarGraficaFacturacionMensual(data.facturacionMensual);
    } else {
        console.log('⚠️ No hay datos de facturación mensual');
        document.getElementById('Sumaim').innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--gray-500);">
                <div style="text-align: center;">
                    <i class="fas fa-info-circle fa-2x" style="margin-bottom: 1rem; color: var(--gray-400);"></i>
                    <p>No hay datos de facturación mensual</p>
                </div>
            </div>
        `;
    }
}

// ===== FUNCIONES PLACEHOLDER PARA GRÁFICAS DE FACTURACIÓN =====

function cargarGraficaFacturasPorEstado(data) {
    console.log('📊 Cargando gráfica de facturas por estado:', data);

    const elemento = document.getElementById('estatus');
    if (!elemento) {
        console.error('❌ No se encontró el elemento #estatus');
        return;
    }

    // Limpiar contenido anterior
    elemento.innerHTML = '';

    try {
        // Preparar datos para ApexCharts
        const labels = data.map(item => item.estado || 'Sin estado');
        const series = data.map(item => parseFloat(item.total) || 0);
        const counts = data.map(item => parseInt(item.cantidad) || 0);

        const options = {
            series: series,
            chart: {
                type: 'pie',
                height: 300
            },
            labels: labels,
            title: {
                text: 'Facturas por Estado',
                align: 'center'
            },
            tooltip: {
                y: {
                    formatter: function(val, opts) {
                        const count = counts[opts.seriesIndex];
                        return `$${val.toLocaleString()} (${count} facturas)`;
                    }
                }
            },
            legend: {
                position: 'bottom'
            }
        };

        const chart = new ApexCharts(elemento, options);
        chart.render();
        console.log('✅ Gráfica de facturas por estado renderizada');

    } catch (error) {
        console.error('❌ Error al crear gráfica de facturas por estado:', error);
        elemento.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--danger-red);">
                <div style="text-align: center;">
                    <i class="fas fa-exclamation-triangle fa-2x" style="margin-bottom: 1rem;"></i>
                    <p>Error al renderizar gráfica</p>
                </div>
            </div>
        `;
    }
}

function cargarGraficaTotalFacturado(data) {
    console.log('📊 Cargando gráfica de total facturado:', data);

    const elemento = document.getElementById('tio');
    if (!elemento) {
        console.error('❌ No se encontró el elemento #tio');
        return;
    }

    // Limpiar contenido anterior
    elemento.innerHTML = '';

    try {
        // Preparar datos para ApexCharts
        const labels = data.map(item => item.sucursal || 'Sin sucursal');
        const series = data.map(item => parseFloat(item.totalFacturado) || 0);

        const options = {
            series: [{
                name: 'Total Facturado',
                data: series
            }],
            chart: {
                type: 'bar',
                height: 300
            },
            xaxis: {
                categories: labels
            },
            title: {
                text: 'Total Facturado por Sucursal',
                align: 'center'
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return `$${val.toLocaleString()}`;
                    }
                }
            }
        };

        const chart = new ApexCharts(elemento, options);
        chart.render();
        console.log('✅ Gráfica de total facturado renderizada');

    } catch (error) {
        console.error('❌ Error al crear gráfica de total facturado:', error);
        elemento.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--danger-red);">
                <div style="text-align: center;">
                    <i class="fas fa-exclamation-triangle fa-2x" style="margin-bottom: 1rem;"></i>
                    <p>Error al renderizar gráfica</p>
                </div>
            </div>
        `;
    }
}

function cargarGraficaFacturacionMensual(data) {
    console.log('📊 Cargando gráfica de facturación mensual:', data);

    const elemento = document.getElementById('Sumaim');
    if (!elemento) {
        console.error('❌ No se encontró el elemento #Sumaim');
        return;
    }

    // Limpiar contenido anterior
    elemento.innerHTML = '';

    try {
        // Preparar datos para ApexCharts
        const meses = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'];
        const datosOrdenados = Array(12).fill(0);

        data.forEach(item => {
            const mesIndex = parseInt(item.mes) - 1;
            if (mesIndex >= 0 && mesIndex < 12) {
                datosOrdenados[mesIndex] = parseFloat(item.totalFacturado) || 0;
            }
        });

        const options = {
            series: [{
                name: 'Facturación Mensual',
                data: datosOrdenados
            }],
            chart: {
                type: 'line',
                height: 350
            },
            xaxis: {
                categories: meses
            },
            title: {
                text: 'Evolución Mensual de Facturación',
                align: 'center'
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return `$${val.toLocaleString()}`;
                    }
                }
            },
            stroke: {
                curve: 'smooth'
            }
        };

        const chart = new ApexCharts(elemento, options);
        chart.render();
        console.log('✅ Gráfica de facturación mensual renderizada');

    } catch (error) {
        console.error('❌ Error al crear gráfica de facturación mensual:', error);
        elemento.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--danger-red);">
                <div style="text-align: center;">
                    <i class="fas fa-exclamation-triangle fa-2x" style="margin-bottom: 1rem;"></i>
                    <p>Error al renderizar gráfica</p>
                </div>
            </div>
        `;
    }
}

// ===== FUNCIONES AUXILIARES PARA VALIDACIONES =====
function validarSeleccionSucursales() {
    const sucursales = document.querySelectorAll('input[name="sucursal"]:checked');
    return sucursales.length > 0;
}

function validarSeleccionFecha() {
    const fecha = document.getElementById('fecha-hoy').value;
    return fecha && fecha.trim() !== '';
}

function validarSeleccionAnio() {
    const year = document.getElementById('year-select').value;
    return year && year.trim() !== '';
}

// ===== FUNCIONES DE SWEETALERT2 =====
function mostrarAlerta(tipo, titulo, mensaje) {
    // Verificar que SweetAlert2 esté disponible
    if (typeof Swal === 'undefined') {
        console.error('SweetAlert2 no está cargado. Usando alert() normal.');
        alert(`${titulo}: ${mensaje}`);
        return;
    }

    Swal.fire({
        icon: tipo, // 'success', 'error', 'warning', 'info'
        title: titulo,
        text: mensaje,
        confirmButtonText: 'Entendido',
        confirmButtonColor: '#007bff',
        customClass: {
            popup: 'swal2-popup-custom',
            title: 'swal2-title-custom',
            content: 'swal2-content-custom'
        },
        showClass: {
            popup: 'animate__animated animate__fadeInDown'
        },
        hideClass: {
            popup: 'animate__animated animate__fadeOutUp'
        }
    });
}

function mostrarAlertaError(mensaje) {
    mostrarAlerta('error', 'Error', mensaje);
}

function mostrarAlertaAdvertencia(mensaje) {
    mostrarAlerta('warning', 'Atención', mensaje);
}

function mostrarAlertaExito(mensaje) {
    mostrarAlerta('success', 'Éxito', mensaje);
}

function mostrarAlertaInfo(mensaje) {
    mostrarAlerta('info', 'Información', mensaje);
}

// Función de prueba para verificar SweetAlert2
function probarSweetAlert() {
    mostrarAlertaExito('¡SweetAlert2 está funcionando correctamente!');
}

// Verificar SweetAlert2 al cargar la página
function verificarSweetAlert() {
    if (typeof Swal !== 'undefined') {
         ('✅ SweetAlert2 cargado correctamente');
        // Mostrar una alerta de bienvenida opcional
        // mostrarAlertaInfo('Dashboard cargado correctamente');
    } else {
        console.error('❌ SweetAlert2 no está disponible');
        alert('Error: SweetAlert2 no se pudo cargar');
    }
}

// ===== FUNCIONES DE FORMATO =====
function formatearPesos(valor) {
    if (valor === null || valor === undefined || isNaN(valor)) {
        return '$0.00';
    }

    const numero = parseFloat(valor);
    return new Intl.NumberFormat('es-MX', {
        style: 'currency',
        currency: 'MXN',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(numero);
}

function formatearPesosCorto(valor) {
    if (valor === null || valor === undefined || isNaN(valor)) {
        return '$0';
    }

    const numero = parseFloat(valor);
    if (numero >= 1000000) {
        return '$' + (numero / 1000000).toFixed(1) + 'M';
    } else if (numero >= 1000) {
        return '$' + (numero / 1000).toFixed(1) + 'K';
    } else {
        return '$' + numero.toFixed(0);
    }
}

// ===== FUNCIONES PARA EVENTOS DE CAMBIO =====
function onSucursalChange() {
     ('Sucursal seleccionada cambiada');
    // Opcional: Auto-actualizar si el usuario lo desea
    // actualizarTodasLasGraficas();
}

function onYearChange() {
     ('Año seleccionado cambiado');
    // Opcional: Auto-actualizar si el usuario lo desea
    // actualizarTodasLasGraficas();
}

function onFechaChange() {
     ('Fecha seleccionada cambiada');
    // Opcional: Auto-actualizar si el usuario lo desea
    // cargarGraficasIngresosDiarios();
}

// ===== FUNCIONES DE UTILIDAD PARA INDICADORES DE CARGA =====
function mostrarIndicadorCarga(selector, mensaje = 'Cargando gráfica...') {
    const elemento = document.querySelector(selector);
    if (elemento) {
        elemento.innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 450px;">
                <div class="text-center">
                    <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                    <p class="mt-2 text-muted fw-bold">${mensaje}</p>
                    <div class="progress mt-2" style="height: 4px; width: 200px; margin: 0 auto;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        `;
    }
}

function ocultarIndicadorCarga(selector) {
    const elemento = document.querySelector(selector);
    if (elemento) {
        elemento.innerHTML = '';
    }
}

// ===== INDICADORES ESPECÍFICOS PARA CADA TIPO DE GRÁFICA =====
function mostrarIndicadorIngresos() {
    mostrarIndicadorCarga('#graficaSucursal', 'Cargando ingresos por sucursal...');
    mostrarIndicadorCarga('#graficaTipoPago', 'Cargando pagos por tipo...');
    mostrarIndicadorCarga('#deudaTotal', 'Cargando deuda por sucursal...');
}

function mostrarIndicadorVentas() {
    mostrarIndicadorCarga('#sumaMontos', 'Cargando ventas mensuales...');
    mostrarIndicadorCarga('#sumaPagos', 'Cargando pagos por sucursal...');
}

function mostrarIndicadorMarcas() {
    mostrarIndicadorCarga('#recuentoMarcas', 'Cargando ventas por marca...');
    mostrarIndicadorCarga('#tratamientoGrafica', 'Cargando modelos y tratamientos...');
}

function mostrarIndicadorFacturacion() {
    mostrarIndicadorCarga('#estatus', 'Cargando facturación por estatus...');
    mostrarIndicadorCarga('#tio', 'Cargando facturación por tipo...');
    mostrarIndicadorCarga('#Sumaim', 'Cargando facturación mensual...');
}

function mostrarIndicadorError(selector, mensaje = 'Error al cargar datos') {
    const elemento = document.querySelector(selector);
    if (elemento) {
        elemento.innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 350px;">
                <div class="text-center">
                    <i class="fa fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                    <p class="mt-3 text-muted">${mensaje}</p>
                    <button class="btn btn-outline-primary btn-sm mt-2" onclick="actualizarTodasLasGraficas()">
                        <i class="fa fa-refresh me-1"></i> Reintentar
                    </button>
                </div>
            </div>
        `;
    }
}

// ===== FUNCIONES PARA BARRA DE PROGRESO GLOBAL =====
function mostrarBarraProgreso() {
    // Crear barra de progreso si no existe
    if (!document.getElementById('dashboard-progress-bar')) {
        const progressHTML = `
            <div id="dashboard-progress-container" class="position-fixed top-0 start-0 w-100" style="z-index: 9999; background: rgba(255,255,255,0.95); padding: 15px;">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <h6 id="progress-text" class="text-center mb-2">Iniciando carga del dashboard...</h6>
                            <div class="progress" style="height: 8px;">
                                <div id="dashboard-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-success"
                                     role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('afterbegin', progressHTML);
    }
}

function actualizarProgreso(porcentaje, mensaje) {
    const progressBar = document.getElementById('dashboard-progress-bar');
    const progressText = document.getElementById('progress-text');

    if (progressBar) {
        progressBar.style.width = porcentaje + '%';
        progressBar.setAttribute('aria-valuenow', porcentaje);
    }

    if (progressText) {
        progressText.textContent = mensaje;
    }
}

function ocultarBarraProgreso() {
    const container = document.getElementById('dashboard-progress-container');
    if (container) {
        container.remove();
    }
}

function mostrarMensajeExito(mensaje) {
    // Crear toast de éxito
    const toastHTML = `
        <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 10000;">
            <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header bg-success text-white">
                    <i class="fa fa-check-circle me-2"></i>
                    <strong class="me-auto">Dashboard</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${mensaje}
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', toastHTML);

    // Auto-remover después de 3 segundos
    setTimeout(() => {
        const toast = document.querySelector('.toast-container');
        if (toast) {
            toast.remove();
        }
    }, 3000);
}

 // Obtener categorías
    function obtenerCategorias() {
        const clases = [...document.querySelectorAll('input[name="clase"]:checked')].map(c => c.value);
        const params = new URLSearchParams();
        clases.forEach(c => params.append('clases[]', c));
        fetch(`{{ path('almacen-obtener-subcategoria') }}?${params.toString()}`)
            .then(res => res.text())
            .then(html => {
                const subcategoriasElement = document.getElementById('subcategorias');
                if (subcategoriasElement) {
                    subcategoriasElement.innerHTML = html;
                    obtenerMarcas();
                }
            })
            .catch(error => console.error('Error al obtener categorías:', error));
    }
 // Obtener tags
    function getTags() {
        const enterpriseId = document.getElementById('idempresa').value;
        fetch(`{{ path('almacen-get-tags') }}?enterpriseId=${enterpriseId}`)
            .then(res => res.text())
            .then(html => {
                const el = document.getElementById('tags');
                if (el) el.innerHTML = html;
            })
            .catch(error => console.error('Error al obtener tags:', error));
    }
    // Obtener clases
    function obtenerClases() {
        const idempresa = document.getElementById('idempresa').value;
        fetch(`{{ path('almacen-obtener-clase') }}?idempresa=${idempresa}`)
            .then(res => res.text())
            .then(html => {
                const clasesElement = document.getElementById('clases');
                if (clasesElement) {
                    clasesElement.innerHTML = html;
                    obtenerCategorias();
                }
            })
            .catch(error => console.error('Error al obtener clases:', error));
    }
    // Obtener sucursales (específico para supervisores)
    function obtenerSucursales() {
        const idempresa = document.getElementById('idempresa').value;
        const isSupervisor = {{ is_supervisor ? 'true' : 'false' }};

        // Usar ruta específica para supervisores o la ruta general para otros roles
        const route = isSupervisor ?
            `{{ path('almacen-obtener-sucursal-supervisor') }}?idempresa=${idempresa}` :
            `{{ path('almacen-obtener-sucursal') }}?idempresa=${idempresa}`;

        fetch(route)
            .then(res => res.text())
            .then(html => {
                const sucursalesElement = document.getElementById('sucursales');
                if (sucursalesElement) {
                    sucursalesElement.innerHTML = html;

                    // Solo obtener categorías si no es supervisor (ya que supervisores solo ven sucursales)
                    if (!isSupervisor) {
                        obtenerCategorias();
                    }

                    // Seleccionar automáticamente todas las sucursales después de cargarlas
                    setTimeout(() => {
                        seleccionarTodasLasSucursales();

                        // Actualizar resumen de sucursales
                        setTimeout(updateSucursalesSummary, 200);

                        // Auto-cargar gráficas si la sección está abierta
                        const detailedAnalysisSection = document.querySelector('.detailed-analysis details');
                        if (detailedAnalysisSection && detailedAnalysisSection.open) {
                            console.log('🔄 Auto-cargando gráficas después de cargar sucursales...');
                            setTimeout(() => {
                                cargarGraficasIngresosDiarios(true); // isAutoLoad = true
                            }, 500);
                        }
                    }, 100);
                }
            })
            .catch(error => console.error('Error al obtener sucursales:', error));
    }
     function cargaDefiltros() {
        const idempresa = document.getElementById('idempresa').value;
        if (idempresa > 0) {
            obtenerSucursales();
            obtenerClases();
            getTags();
        } else {
            ['clases', 'subcategorias', 'marcas', 'sucursales'].forEach(id => {
                document.getElementById(id).innerHTML = ''; // Limpia el contenido de los filtros
            });
        }
    }
      // Cambia el estado de los checkboxes
      function toggleChecks(name, source) {
  document.querySelectorAll(`input[name="${name}"]`).forEach(cb => cb.checked = source.checked);
}
    // Configuración de datetimepicker con Moment.js
    jQuery(function ($) {
        // Verificar que Moment.js esté disponible
        if (typeof moment !== 'undefined') {
            moment.locale('es');

            $('#fecha-inicio-rango-dia').datetimepicker({
                format: 'DD/MM/YYYY',
                locale: 'es',
                showTodayButton: true,
                showClear: true,
                showClose: true,
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-chevron-up",
                    down: "fa fa-chevron-down",
                    previous: "fa fa-chevron-left",
                    next: "fa fa-chevron-right",
                    today: "fa fa-calendar-check-o",
                    clear: "fa fa-trash",
                    close: "fa fa-times"
                }
            });

            $('#fecha-fin-rango-dia').datetimepicker({
                format: 'DD/MM/YYYY',
                locale: 'es',
                showTodayButton: true,
                showClear: true,
                showClose: true,
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-chevron-up",
                    down: "fa fa-chevron-down",
                    previous: "fa fa-chevron-left",
                    next: "fa fa-chevron-right",
                    today: "fa fa-calendar-check-o",
                    clear: "fa fa-trash",
                    close: "fa fa-times"
                }
            });
        } else {
            console.warn('Moment.js no está disponible. Los datepickers no funcionarán correctamente.');
        }
    });
     function resetRangoFechaDias() {
            $("#fecha-inicio-rango-dia").val("");
            $("#fecha-fin-rango-dia").val("");
        }
 // Cargar resumen de ingresos
    window.cargarResumenIngresos = function () {
        const todayDate = document.getElementById('fecha-hoy').value;
        const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')].map(cb => cb.value).join(',');
        if (!todayDate || !sucursales) return;

        $.get('/cliente-api/get-ingreso-diario-overview', { todayDate, sucursales }, function (response) {
            // Validar si hay datos válidos
            const ventaData = response.sumaVentaCobrar?.totalVenta;
            const porCobrarData = response.sumaVentaCobrar?.porCobrar;
            const cobradoData = response.sumaPagos?.totalCobrado;

            // Función para mostrar valor o mensaje de sin datos
            function mostrarValor(valor, elemento) {
                if (valor === null || valor === undefined || isNaN(parseFloat(valor))) {
                    $(elemento).text('Sin información');
                } else {
                    const numero = parseFloat(valor);
                    $(elemento).text(formatearPesos(numero));
                }
            }

            mostrarValor(ventaData, '#ventas-total');
            mostrarValor(porCobrarData, '#por-cobrar-total');
            mostrarValor(cobradoData, '#pagos-total');
        }).fail(function() {
            // En caso de error en la petición
            $('#ventas-total').text('Sin información');
            $('#por-cobrar-total').text('Sin información');
            $('#pagos-total').text('Sin información');
        });
    };
    $('#fecha-hoy, input[name="sucursal"]').on('change', function () {
        cargarResumenIngresos();
       // cargarGraficasDetalles();
    });
    document.addEventListener('DOMContentLoaded', () => {
    // Establecer las fechas al día de hoy al cargar la página
    setTodayDate();

    // Configuración automática inicial
    inicializarDashboardAutomatico();

    // Escuchar los cambios de fechas
    const fechaHoy = document.getElementById('fecha-hoy');
    if (fechaHoy) {
        fechaHoy.addEventListener('change', () => {
            refreshGraphs();
        });
    }

    // Escuchar cambios en sucursales o otros filtros
    document.addEventListener('change', function(e) {
        if (e.target.matches('input[name="sucursal"], input[name="clase"]')) {
            refreshGraphs();
        }
    });
});

// Establece las fechas de "hoy" en los campos correspondientes
function setTodayDate() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const formattedDate = `${year}-${month}-${day}`;
    const formattedDateText = `${day}/${month}/${year}`; // Formato para campos de texto

     ('Estableciendo fecha de hoy:', formattedDate);

    // Establecer fecha en todos los campos de fecha
    const fechaHoy = document.getElementById('fecha-hoy');
    const fechaInicio = document.getElementById('fecha-inicio-rango-dia');
    const fechaFin = document.getElementById('fecha-fin-rango-dia');

    if (fechaHoy) fechaHoy.value = formattedDate; // Campo tipo date
    if (fechaInicio) fechaInicio.value = formattedDateText; // Campo tipo text
    if (fechaFin) fechaFin.value = formattedDateText; // Campo tipo text
}

// ===== FUNCIÓN PARA INICIALIZAR DASHBOARD AUTOMÁTICAMENTE =====
function inicializarDashboardAutomatico() {
     ('Inicializando dashboard automáticamente...');

    // Obtener el elemento de empresa (puede ser un select o un input hidden)
    const empresaElement = document.getElementById('idempresa');
    
    if (!empresaElement) {
        console.error('No se encontró el elemento de empresa');
        return;
    }
    
    // Verificar si es un select (para administradores) o un input hidden (para supervisores)
    const isSelect = empresaElement.tagName.toLowerCase() === 'select';
    
    if (isSelect && empresaElement.options.length > 1) {
        // Para administradores: Seleccionar la primera empresa (índice 1, ya que 0 es "Seleccione una empresa")
        empresaElement.selectedIndex = 1;
        
        // Disparar el evento change para cargar los filtros
        const event = new Event('change', { bubbles: true });
        empresaElement.dispatchEvent(event);
        
         ('Empresa seleccionada automáticamente (admin):', empresaElement.value);
    } else if (!isSelect) {
        // Para supervisores: La empresa ya está hardcodeada en el input hidden
         ('Empresa hardcodeada (supervisor):', empresaElement.value);
        
        // Cargar los filtros directamente
        cargaDefiltros();
    }

    // Esperar un momento para que se carguen las sucursales y luego seleccionarlas todas
    setTimeout(() => {
        seleccionarTodasLasSucursales();
        // Cargar datos iniciales después de seleccionar sucursales
        setTimeout(() => {
            cargarDatosIniciales();
        }, 1000);
    }, 1500);
}

// ===== FUNCIÓN PARA SELECCIONAR TODAS LAS SUCURSALES =====
function seleccionarTodasLasSucursales() {
     ('Seleccionando todas las sucursales...');

    const checkboxesSucursales = document.querySelectorAll('input[name="sucursal"]');
    checkboxesSucursales.forEach(checkbox => {
        checkbox.checked = true;
    });

     ('Sucursales seleccionadas:', checkboxesSucursales.length);
}

// ===== FUNCIÓN PARA CARGAR DATOS INICIALES =====
function cargarDatosIniciales() {
     ('Cargando datos iniciales del dashboard...');

    // Cargar datos reales de ingresos diarios
    cargarResumenIngresos();
    cargarGraficasIngresosDiarios();

    // Cargar otras gráficas con un pequeño delay
    setTimeout(() => {
        buscarVentasAnuales();
        buscarDatosFacturacion();
    }, 1000);
}



// ===== FUNCIÓN PARA REFRESCAR TODAS LAS GRÁFICAS =====
function refreshGraphs() {
    console.log('Refrescando todas las gráficas...');

    // Validar datos básicos antes de refrescar
    if (!validarSeleccionAnio()) {
        mostrarAlertaAdvertencia('Por favor, seleccione un año antes de refrescar.');
        return;
    }

    if (!validarSeleccionSucursales()) {
        mostrarAlertaAdvertencia('Por favor, seleccione al menos una sucursal antes de refrescar.');
        return;
    }

    // Usar el sistema de carga secuencial
    actualizarTodasLasGraficas();
}
// ===== FUNCIONES PARA CARGAR GRÁFICAS DE INGRESOS DIARIOS (OPTIMIZADA) =====
function cargarGraficasIngresosDiarios(isAutoLoad = false) {
    // Usar la fecha específica del día seleccionado
    const todayDate = document.getElementById('fecha-hoy').value;
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')].map(cb => cb.value).join(',');
    const sucursalesDisponibles = document.querySelectorAll('input[name="sucursal"]').length;

    console.log('🔄 Iniciando carga de gráficas de ingresos diarios...');
    console.log('📅 Fecha seleccionada:', todayDate);
    console.log('🏪 Sucursales seleccionadas:', sucursales);
    console.log('🏪 Sucursales disponibles:', sucursalesDisponibles);
    console.log('🤖 Es carga automática:', isAutoLoad);

    // Validación de campos mejorada
    if (!todayDate) {
        console.warn('⚠️ Falta fecha');
        if (!isAutoLoad) {
            mostrarAlertaAdvertencia('Por favor, seleccione una fecha.');
        }
        return;
    }

    if (!sucursales) {
        console.warn('⚠️ No hay sucursales seleccionadas');

        // Si es carga automática y no hay sucursales disponibles, esperar
        if (isAutoLoad && sucursalesDisponibles === 0) {
            console.log('⏳ Carga automática: esperando a que se carguen las sucursales...');
            setTimeout(() => {
                cargarGraficasIngresosDiarios(true);
            }, 2000);
            return;
        }

        // Si es carga manual o ya hay sucursales disponibles pero ninguna seleccionada
        if (!isAutoLoad) {
            mostrarAlertaAdvertencia('Por favor, seleccione al menos una sucursal.');
        } else {
            console.log('⏳ Carga automática: no hay sucursales seleccionadas, saltando...');
        }
        return;
    }

    // Mostrar indicadores de carga optimizados
    const graficaSucursal = document.getElementById('graficaSucursal');
    const graficaTipoPago = document.getElementById('graficaTipoPago');
    const deudaTotal = document.getElementById('deudaTotal');

    if (graficaSucursal) {
        graficaSucursal.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 300px; color: var(--gray-500);"><div style="text-align: center;"><i class="fas fa-spinner fa-spin fa-2x" style="margin-bottom: 1rem; color: var(--primary-blue);"></i><p>Cargando ingresos por sucursal...</p></div></div>';
    }
    if (graficaTipoPago) {
        graficaTipoPago.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 300px; color: var(--gray-500);"><div style="text-align: center;"><i class="fas fa-spinner fa-spin fa-2x" style="margin-bottom: 1rem; color: var(--primary-blue);"></i><p>Cargando tipos de pago...</p></div></div>';
    }
    if (deudaTotal) {
        deudaTotal.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 300px; color: var(--gray-500);"><div style="text-align: center;"><i class="fas fa-spinner fa-spin fa-2x" style="margin-bottom: 1rem; color: var(--primary-blue);"></i><p>Cargando deuda por sucursal...</p></div></div>';
    }

    // Usar fetch en lugar de jQuery para mejor rendimiento
    const startTime = performance.now();

    fetch(`/cliente-api/get-ingreso-diario-details?todayDate=${todayDate}&sucursales=${sucursales}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(response => {
            const endTime = performance.now();
            console.log(`✅ Datos recibidos en ${(endTime - startTime).toFixed(2)}ms:`, response);

            // Cargar gráficas secuencialmente para evitar conflictos
            console.log('📊 Iniciando carga de gráficas...');

            // 1. Cargar gráfica de ingresos por sucursal
            try {
                cargarGraficaIngresosPorSucursal(response.dataPerSucursal);
                console.log('✅ Gráfica de ingresos por sucursal iniciada');
            } catch (error) {
                console.error('❌ Error en gráfica de ingresos:', error);
            }

            // 2. Cargar gráfica de tipos de pago (con delay para evitar conflictos)
            setTimeout(() => {
                try {
                    cargarGraficaPagosPorTipo(response.dataPerPaymentType);
                    console.log('✅ Gráfica de tipos de pago iniciada');
                } catch (error) {
                    console.error('❌ Error en gráfica de tipos de pago:', error);
                }
            }, 200);

            // 3. Cargar gráfica de deuda por sucursal
            setTimeout(() => {
                try {
                    cargarGraficaDeudaPorSucursal(response.dataPerSucursal);
                    console.log('✅ Gráfica de deuda por sucursal iniciada');
                } catch (error) {
                    console.error('❌ Error en gráfica de deuda:', error);
                }
            }, 400);

            console.log('🎯 Todas las gráficas programadas para carga');

        })
        .catch(error => {
            console.error('❌ Error al cargar gráficas de ingresos diarios:', error);
            mostrarAlertaError('Hubo un error al obtener los datos. Por favor, intente de nuevo.');

            // Mostrar mensajes de error en las gráficas
            if (graficaSucursal) {
                graficaSucursal.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 300px; color: var(--danger-red);"><div style="text-align: center;"><i class="fas fa-exclamation-triangle fa-2x" style="margin-bottom: 1rem;"></i><p>Error al cargar datos</p></div></div>';
            }
            if (graficaTipoPago) {
                graficaTipoPago.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 300px; color: var(--danger-red);"><div style="text-align: center;"><i class="fas fa-exclamation-triangle fa-2x" style="margin-bottom: 1rem;"></i><p>Error al cargar datos</p></div></div>';
            }
            if (deudaTotal) {
                deudaTotal.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 300px; color: var(--danger-red);"><div style="text-align: center;"><i class="fas fa-exclamation-triangle fa-2x" style="margin-bottom: 1rem;"></i><p>Error al cargar datos</p></div></div>';
            }
        });
}

function cargarGraficaIngresosPorSucursal(data) {
    if (!data || data.length === 0) {
        document.querySelector("#graficaSucursal").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 550px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin información de ingresos disponible</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#graficaSucursal');
        return;
    }

    // Filtrar datos válidos
    const datosValidos = data.filter(item => {
        const total = parseFloat(item.totalPagado);
        return !isNaN(total) && total > 0;
    });

    if (datosValidos.length === 0) {
        document.querySelector("#graficaSucursal").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 550px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin ingresos registrados para el período seleccionado</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#graficaSucursal');
        return;
    }

    const labels = datosValidos.map(item => item.sucursal || 'Sin nombre');
    const series = datosValidos.map(item => parseFloat(item.totalPagado));

    const chart = new ApexCharts(document.querySelector("#graficaSucursal"), {
        series: series,
        chart: {
            type: 'pie',
            height: 550,
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        labels: labels,
        title: {
            text: 'Ingresos por Sucursal',
            align: 'center',
            style: { fontSize: '16px', fontWeight: 'bold' }
        },
        dataLabels: {
            enabled: false // Deshabilitado para evitar advertencias de performance
        },
        tooltip: {
            y: { formatter: val => formatearPesos(val) }
        },
        legend: {
            position: 'bottom',
            fontSize: '12px',
            formatter: function (val, opts) {
                const value = opts.w.globals.series[opts.seriesIndex];
                return `${val}: ${formatearPesosCorto(value)}`;
            }
        },
        colors: ['#008FFB', '#00E396', '#FEB019', '#FF4560', '#775DD0', '#546E7A', '#26a69a', '#D10CE8'],
        responsive: [{
            breakpoint: 768,
            options: {
                chart: { height: 400 },
                legend: { position: 'bottom' }
            }
        }]
    });

    ocultarIndicadorCarga('#graficaSucursal');
    chart.render();
}

// Flags para evitar múltiples cargas simultáneas
let isLoadingTiposPago = false;
let tiposPagoCallCount = 0;

// Sistema de logging para detectar llamadas duplicadas
function logFunctionCall(functionName, data) {
    const timestamp = new Date().toISOString();
    console.log(`🕐 [${timestamp}] ${functionName} llamada #${++window[functionName + 'CallCount'] || 1}`);
    if (data) {
        console.log(`📊 Datos recibidos:`, data);
    }
}

function cargarGraficaPagosPorTipo(data) {
    // Incrementar contador y loggear llamada
    tiposPagoCallCount++;
    console.log(`🔄 === PAGOS POR TIPO - LLAMADA #${tiposPagoCallCount} ===`);
    console.log('Datos recibidos para pagos por tipo:', data);
    console.log('Tipo de datos:', typeof data);
    console.log('Es array:', Array.isArray(data));

    // Prevenir múltiples cargas simultáneas
    if (isLoadingTiposPago) {
        console.log('⚠️ Ya se está cargando la gráfica de tipos de pago, saltando...');
        return;
    }
    isLoadingTiposPago = true;

    // Obtener elemento y limpiar contenido anterior
    const elemento = document.querySelector("#graficaTipoPago");
    if (!elemento) {
        console.error('❌ No se encontró el elemento #graficaTipoPago');
        isLoadingTiposPago = false;
        return;
    }

    // LIMPIAR CONTENIDO ANTERIOR (esto soluciona el spinner infinito)
    elemento.innerHTML = '';
    console.log('🧹 Contenido anterior limpiado');

    if (!data || data.length === 0) {
        elemento.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 300px; color: var(--gray-500);">
                <div style="text-align: center;">
                    <i class="fas fa-info-circle fa-2x" style="margin-bottom: 1rem; color: var(--gray-400);"></i>
                    <p>Sin información de pagos disponible</p>
                </div>
            </div>
        `;
        isLoadingTiposPago = false;
        return;
    }

    // Mapear tipos de pago a nombres más claros en español
    function traducirTipoPago(tipo) {
        const traducciones = {
            'convenio': 'Convenio',
            'efectivo': 'Efectivo',
            'tarjeta débito': 'Tarjeta Débito',
            'tarjeta de crédito': 'Tarjeta Crédito',
            'transferencia': 'Transferencia',
            'pago descuento nomina': 'Descuento Nómina',
            'depósito': 'Depósito',
            'prestación': 'Prestación',
            'vales': 'Vales',
            'link clip': 'Link CLIP',
            // Fallbacks para otros formatos
            'cash': 'Efectivo',
            'card': 'Tarjeta',
            'credit': 'Crédito',
            'transfer': 'Transferencia',
            'check': 'Cheque',
            'tarjeta': 'Tarjeta',
            'credito': 'Crédito',
            'cheque': 'Cheque'
        };
        const tipoLower = tipo?.toLowerCase();
         (`Traduciendo tipo: "${tipo}" -> "${traducciones[tipoLower] || tipo}"`);
        return traducciones[tipoLower] || tipo || 'Otro';
    }

    // Procesar datos con la estructura exacta que llega: {paymentType: "...", cobrado: "..."}
    const datosValidos = data.filter(item => {
        const valor = parseFloat(item.cobrado || 0);
        const tipo = item.paymentType;
        return !isNaN(valor) && valor > 0 && tipo;
    }).map(item => ({
        tipo: item.paymentType,
        valor: parseFloat(item.cobrado)
    }));

    console.log('Datos válidos de pagos procesados:', datosValidos);
    console.log('Cantidad de datos válidos:', datosValidos.length);

    if (datosValidos.length === 0) {
        elemento.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 300px; color: var(--gray-500);">
                <div style="text-align: center;">
                    <i class="fas fa-info-circle fa-2x" style="margin-bottom: 1rem; color: var(--gray-400);"></i>
                    <p>Sin datos de pagos válidos para mostrar</p>
                </div>
            </div>
        `;
        isLoadingTiposPago = false;
        return;
    }

    console.log('Datos válidos antes de mapear:', datosValidos);

    const labels = datosValidos.map(item => {
        const tipoOriginal = item.tipo;
        const tipoTraducido = traducirTipoPago(item.tipo);
        console.log(`Mapeando: "${tipoOriginal}" -> "${tipoTraducido}"`);
        return tipoTraducido;
    });
    const series = datosValidos.map(item => item.valor);

    console.log('Labels finales de tipos de pago:', labels);
    console.log('Series finales de tipos de pago:', series);

    // Crear datos en formato correcto para ApexCharts barras horizontales
    const chartData = labels.map((label, index) => ({
        x: label,
        y: series[index]
    }));

    console.log('Datos formateados para ApexCharts:', chartData);

    const chartConfig = {
        series: [{
            name: 'Total Cobrado',
            data: chartData
        }],
        chart: {
            type: 'bar',
            height: 500,
            toolbar: {
                show: true
            },
            fontFamily: 'Arial, sans-serif'
        },
        title: {
            text: 'Pagos por Tipo',
            align: 'center',
            style: {
                fontSize: '16px',
                fontWeight: 'bold'
            }
        },
        plotOptions: {
            bar: {
                horizontal: true,
                borderRadius: 4,
                dataLabels: {
                    position: 'right'
                },
                barHeight: '70%'
            }
        },
        dataLabels: {
            enabled: false // Deshabilitado para evitar advertencias de performance
        },
        xaxis: {
            labels: {
                formatter: function(val) {
                    return '$' + new Intl.NumberFormat('es-MX').format(val);
                },
                style: {
                    fontSize: '12px'
                }
            },
            title: {
                text: 'Monto Cobrado'
            }
        },
        yaxis: {
            labels: {
                style: {
                    fontSize: '12px',
                    fontWeight: '600',
                    colors: ['#333']
                },
                maxWidth: 200
            },
            title: {
                text: 'Tipos de Pago'
            }
        },
        tooltip: {
            y: {
                formatter: function(val) {
                    return '$' + new Intl.NumberFormat('es-MX', { minimumFractionDigits: 2 }).format(val);
                }
            }
        },
        colors: ['#008FFB'],
        grid: {
            borderColor: '#e7e7e7',
            row: {
                colors: ['#f3f3f3', 'transparent'],
                opacity: 0.5
            }
        }
    };

    console.log('Configuración completa de la gráfica:', chartConfig);
    console.log('Series de datos con nombres:', chartConfig.series);
    console.log('Datos individuales:', chartConfig.series[0].data);

    console.log('📊 Creando gráfica de ApexCharts...');

    try {
        const chart = new ApexCharts(elemento, chartConfig);

        chart.render().then(() => {
            console.log('✅ Gráfica de Pagos por Tipo renderizada exitosamente');
            console.log('📊 Categorías mostradas:', labels);
            console.log('💰 Valores mostrados:', series);
            isLoadingTiposPago = false; // Liberar flag al completar
        }).catch(error => {
            console.error('❌ Error al renderizar la gráfica:', error);
            elemento.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 300px; color: var(--danger-red);">
                    <div style="text-align: center;">
                        <i class="fas fa-exclamation-triangle fa-2x" style="margin-bottom: 1rem;"></i>
                        <p>Error al renderizar la gráfica</p>
                    </div>
                </div>
            `;
            isLoadingTiposPago = false; // Liberar flag en caso de error
        });
    } catch (error) {
        console.error('❌ Error al crear la gráfica:', error);
        elemento.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 300px; color: var(--danger-red);">
                <div style="text-align: center;">
                    <i class="fas fa-exclamation-triangle fa-2x" style="margin-bottom: 1rem;"></i>
                    <p>Error al crear la gráfica</p>
                </div>
            </div>
        `;
        isLoadingTiposPago = false; // Liberar flag en caso de error
    }
}

function cargarGraficaDeudaPorSucursal(data) {
    const labels = data.map(item => item.sucursal);
    const series = data.map(item => {
        const total = parseFloat(item.totalDeuda);
        return isNaN(total) || total < 0 ? 0 : total;
    });

    const chart = new ApexCharts(document.querySelector("#deudaTotal"), {
        series: [{ name: 'Total Deuda', data: series }],
        chart: { type: 'bar', height: 450 },
        title: { text: 'Deuda por Sucursal', align: 'center' },
        plotOptions: {
            bar: {
                borderRadius: 4,
                dataLabels: { position: 'top' }
            }
        },
        dataLabels: {
            enabled: true,
            formatter: val => formatearPesosCorto(val),
            offsetY: -20,
            style: { fontSize: '12px', colors: ['#304758'] }
        },
        xaxis: {
            categories: labels,
            labels: { style: { fontSize: '12px' } }
        },
        yaxis: {
            labels: {
                formatter: val => formatearPesosCorto(val)
            }
        },
        tooltip: {
            y: { formatter: val => formatearPesos(val) }
        }
    });

    ocultarIndicadorCarga('#deudaTotal');
    chart.render();
}
// Función para convertir la fecha al formato YYYY-MM-DD
function formatDate(date) {
    const [day, month, year] = date.split('/');
    return `${year}-${month}-${day}`;  // Convierte al formato YYYY-MM-DD
}
// ===== FUNCIÓN PARA POBLAR SELECT DE AÑOS =====
function poblarSelectAnios() {
    const selectVentas = document.getElementById('year-select');
    const selectFacturacion = document.getElementById('year-select-facturacion');
    const anioActual = new Date().getFullYear();
    const anioInicio = 2020;

    // Función para poblar un select específico
    function poblarSelect(select) {
        if (!select) return;

        // Limpiar opciones anteriores
        select.innerHTML = '';

        for (let anio = anioActual; anio >= anioInicio; anio--) {
            const opcion = document.createElement('option');
            opcion.value = anio;
            opcion.textContent = anio;
            if (anio === anioActual) {
                opcion.selected = true;
            }
            select.appendChild(opcion);
        }
    }

    // Poblar ambos selects
    poblarSelect(selectVentas);
    poblarSelect(selectFacturacion);
}
// Ejecutar cuando cargue el DOM
document.addEventListener('DOMContentLoaded', () => {
    poblarSelectAnios();

    // Cargar gráficas por defecto con el año actual
    setTimeout(() => {
        cargarGraficasVentasAnuales();
    }, 1000);

    // Escuchar cambios en el select de año de ventas
    const yearSelectVentas = document.getElementById('year-select');
    if (yearSelectVentas) {
        yearSelectVentas.addEventListener('change', () => {
            cargarGraficasVentasAnuales();
            cargarGraficasMarcasYModelos();
        });
    }

    // Escuchar cambios en el select de año de facturación
    const yearSelectFacturacion = document.getElementById('year-select-facturacion');
    if (yearSelectFacturacion) {
        yearSelectFacturacion.addEventListener('change', () => {
            cargarGraficasFacturacion();
        });
    }
});

// ===== FUNCIONES PARA CARGAR GRÁFICAS DE VENTAS ANUALES =====
function cargarGraficasVentasAnuales() {
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')].map(cb => cb.value).join(',');
    const year = document.getElementById('year-select').value;

    

     ('Cargando gráficas de ventas anuales...');
     ('Año seleccionado:', year);
     ('Sucursales seleccionadas:', sucursales);

    // Mostrar indicadores de carga
    mostrarIndicadorCarga('#sumaMontos');
    mostrarIndicadorCarga('#sumaPagos');

    $.get(`/cliente-api/get-ingreso-anual-overview?year=${year}&sucursales=${sucursales}`, function (response) {
        // Gráfica de Ventas Mensuales (Bar Chart)
        cargarGraficaVentasMensuales(response.dataPerMonth);

        // Gráfica de Pagos por Sucursal (Pie Chart)
        cargarGraficaPagosPorSucursal(response.dataPerBranch);

    }).fail(function (error) {
        console.error('Error al cargar gráficas de ventas anuales:', error);
        mostrarAlertaError('Hubo un error al obtener los datos. Por favor, intente de nuevo.');
        ocultarIndicadorCarga('#sumaMontos');
        ocultarIndicadorCarga('#sumaPagos');
    });
}

function cargarGraficaVentasMensuales(data) {
    const nombresMeses = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
                          'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'];

    const ordenados = data.sort((a, b) => parseInt(a.ventaMonth) - parseInt(b.ventaMonth));
    const labels = ordenados.map(item => nombresMeses[parseInt(item.ventaMonth) - 1] || 'Sin Mes');
    const series = ordenados.map(item => parseFloat(item.cobrado));

    const chart = new ApexCharts(document.querySelector("#sumaMontos"), {
        series: [{
            name: 'Ventas Mensuales',
            data: series
        }],
        chart: {
            type: 'bar',
            height: 450
        },
        title: { text: 'Ventas Mensuales', align: 'center' },
        plotOptions: {
            bar: {
                borderRadius: 4,
                horizontal: false
            }
        },
        colors: ['#4CAF50'],
        dataLabels: {
            enabled: true,
            formatter: val => formatearPesosCorto(val)
        },
        xaxis: {
            categories: labels
        },
        yaxis: {
            labels: {
                formatter: value => formatearPesosCorto(value)
            }
        },
        tooltip: {
            y: {
                formatter: val => formatearPesos(val)
            }
        }
    });

    ocultarIndicadorCarga('#sumaMontos');
    chart.render();
}

function cargarGraficaPagosPorSucursal(data) {
    const labels = data.map(item => item.sucursal);
    const series = data.map(item => {
        const total = parseFloat(item.cobrado);
        return isNaN(total) || total < 0 ? 0 : total;
    });

    const chart = new ApexCharts(document.querySelector("#sumaPagos"), {
        series: series,
        chart: { type: 'pie', height: 450 },
        labels: labels,
        title: { text: 'Pagos por Sucursal', align: 'center' },
        dataLabels: {
            enabled: true,
            formatter: function (val, opts) {
                const value = opts.w.globals.series[opts.seriesIndex];
                return `${val.toFixed(1)}%\n${formatearPesosCorto(value)}`;
            },
            style: { fontSize: '12px', fontWeight: 'bold', colors: ['#fff'] }
        },
        legend: {
            formatter: function (val, opts) {
                const value = opts.w.globals.series[opts.seriesIndex];
                return `${val}: ${formatearPesosCorto(value)}`;
            }
        },
        tooltip: {
            y: { formatter: val => formatearPesos(val) }
        },
        responsive: [{
            breakpoint: 480,
            options: { chart: { height: 350 }, legend: { position: 'bottom' } }
        }]
    });

    ocultarIndicadorCarga('#sumaPagos');
    chart.render();
}
// ===== FUNCIONES PARA CARGAR GRÁFICAS DE MARCAS Y MODELOS =====
function cargarGraficasMarcasYModelos() {
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')].map(cb => cb.value).join(',');
    const year = document.getElementById('year-select').value;

    if (!year || !sucursales) {
        mostrarAlertaAdvertencia('Por favor, seleccione un año y las sucursales.');
        return;
    }

     ('Cargando gráficas de marcas y modelos...');
     ('Año:', year, 'Sucursales:', sucursales);

    // Mostrar indicadores de carga
    mostrarIndicadorCarga('#recuentoMarcas');
    mostrarIndicadorCarga('#tratamientoGrafica');

    $.get(`/cliente-api/get-ingreso-anual-details?year=${year}&sucursales=${sucursales}`, function (response) {
        // Cargar gráfica de marcas
        cargarGraficaVentasPorMarca(response.marcas);

        // Cargar gráfica de modelos y tratamientos
        cargarGraficaModelosYTratamientos(response.dtm);

    }).fail(function (error) {
        console.error('Error al cargar gráficas de marcas y modelos:', error);
        mostrarAlertaError('Hubo un error al obtener los datos. Por favor, intente de nuevo.');
        ocultarIndicadorCarga('#recuentoMarcas');
        ocultarIndicadorCarga('#tratamientoGrafica');
    });
}

function cargarGraficaVentasPorMarca(data) {
     ('=== VENTAS POR MARCA - DEBUG ===');
     ('Datos recibidos para ventas por marca:', data);
     ('Tipo de datos:', typeof data);
     ('Es array:', Array.isArray(data));

    if (!data || data.length === 0) {
        document.querySelector("#recuentoMarcas").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 450px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin información de marcas disponible</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#recuentoMarcas');
        return;
    }

    // Procesar datos con la estructura exacta que llega: {marca: "...", vendidos: ...}
    const datosValidos = data.filter(item => {
        const vendidos = parseInt(item.vendidos) || 0;
        const marca = item.marca;
        return vendidos > 0 && marca;
    }).map(item => ({
        marca: item.marca,
        vendidos: parseInt(item.vendidos)
    }));

     ('Datos válidos de marcas procesados:', datosValidos);

    if (datosValidos.length === 0) {
        document.querySelector("#recuentoMarcas").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 450px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin datos de ventas válidos</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#recuentoMarcas');
        return;
    }

    // Mostrar TODAS las marcas ordenadas por ventas (de mayor a menor)
    const datosOrdenados = datosValidos
        .sort((a, b) => b.vendidos - a.vendidos);

    // Para barras horizontales, necesitamos crear un array de objetos con x e y
    const chartData = datosOrdenados.map(item => ({
        x: item.marca,
        y: item.vendidos
    }));

     ('Datos formateados para ApexCharts (marcas):', chartData);

    function formatearUnidades(val) {
        if (val >= 1000000) {
            return (val / 1000000).toFixed(1) + ' millones';
        } else if (val >= 1000) {
            return (val / 1000).toFixed(1) + ' mil';
        } else {
            return val.toLocaleString() + ' uds';
        }
    }

    const chart = new ApexCharts(document.querySelector("#recuentoMarcas"), {
        series: [{ name: 'Unidades Vendidas', data: chartData }],
        chart: {
            type: 'bar',
            height: Math.max(800, datosOrdenados.length * 25), // Altura dinámica basada en número de marcas
            toolbar: {
                show: true,
                tools: {
                    zoom: true,
                    zoomin: true,
                    zoomout: true,
                    pan: true,
                    reset: true
                }
            },
            zoom: {
                enabled: true,
                type: 'x',
                autoScaleYaxis: true
            },
            fontFamily: 'Arial, sans-serif'
        },
        title: { text: 'Ventas por Marca', align: 'center' },
        plotOptions: {
            bar: {
                horizontal: true,
                borderRadius: 6,
                dataLabels: { position: 'right' },
                barHeight: '70%',
                distributed: false
            }
        },
        dataLabels: {
            enabled: true,
            formatter: val => formatearUnidades(val),
            offsetX: 8,
            style: {
                fontSize: '12px',
                colors: ['#304758'],
                fontWeight: 'bold'
            }
        },
        xaxis: {
            labels: {
                formatter: val => formatearUnidades(val)
            }
        },
        yaxis: {
            labels: {
                style: {
                    fontSize: '11px',
                    fontWeight: '600',
                    colors: ['#333']
                },
                maxWidth: 200,
                show: true,
                align: 'right',
                minWidth: 0,
                offsetX: 0,
                offsetY: 0,
                rotate: 0
            }
        },
        tooltip: {
            y: { formatter: val => formatearUnidades(val) + ' vendidas' }
        },
        grid: {
            show: true,
            borderColor: '#e7e7e7',
            strokeDashArray: 0,
            position: 'back',
            xaxis: {
                lines: {
                    show: true
                }
            },
            yaxis: {
                lines: {
                    show: false
                }
            },
            row: {
                colors: undefined,
                opacity: 0.5
            },
            column: {
                colors: undefined,
                opacity: 0.5
            },
            padding: {
                top: 0,
                right: 0,
                bottom: 0,
                left: 0
            }
        }
    });

    ocultarIndicadorCarga('#recuentoMarcas');
    chart.render();
     ('Gráfica de Ventas por Marca renderizada con datos:', chartData);
}

function cargarGraficaModelosYTratamientos(data) {
     ('Datos recibidos para modelos y tratamientos:', data);

    if (!data || data.length === 0) {
        document.querySelector("#tratamientoGrafica").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 550px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin información de modelos disponible</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#tratamientoGrafica');
        return;
    }

    // Procesar datos con la estructura exacta que llega: {modelo: "...", vendidos: ...}
    const datosValidos = data.filter(item => {
        const vendidos = parseInt(item.vendidos) || 0;
        const modelo = item.modelo;
        return vendidos > 0 && modelo;
    }).map(item => ({
        modelo: item.modelo,
        vendidos: parseInt(item.vendidos)
    }));

     ('Datos válidos de modelos procesados:', datosValidos);

    if (datosValidos.length === 0) {
        document.querySelector("#tratamientoGrafica").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 550px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin datos de modelos válidos</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#tratamientoGrafica');
        return;
    }

    // Mostrar TODOS los modelos ordenados por ventas (de mayor a menor)
    const datosOrdenados = datosValidos
        .sort((a, b) => b.vendidos - a.vendidos);

    // Para barras horizontales, necesitamos crear un array de objetos con x e y
    const chartData = datosOrdenados.map(item => ({
        x: item.modelo,
        y: item.vendidos
    }));

     ('Datos formateados para ApexCharts (modelos):', chartData);

    function formatearUnidades(val) {
        if (val >= 1000000) {
            return (val / 1000000).toFixed(1) + ' millones';
        } else if (val >= 1000) {
            return (val / 1000).toFixed(1) + ' mil';
        } else {
            return val.toLocaleString() + ' uds';
        }
    }

    const chart = new ApexCharts(document.querySelector("#tratamientoGrafica"), {
        series: [{ name: 'Unidades Vendidas', data: chartData }],
        chart: {
            type: 'bar',
            height: Math.max(1200, datosOrdenados.length * 20), // Altura dinámica basada en número de modelos
            toolbar: {
                show: true,
                tools: {
                    zoom: true,
                    zoomin: true,
                    zoomout: true,
                    pan: true,
                    reset: true
                }
            },
            zoom: {
                enabled: true,
                type: 'x',
                autoScaleYaxis: true
            },
            fontFamily: 'Arial, sans-serif'
        },
        title: { text: 'Modelos y Tratamientos', align: 'center' },
        plotOptions: {
            bar: {
                horizontal: true,
                borderRadius: 6,
                dataLabels: { position: 'right' },
                barHeight: '65%'
            }
        },
        dataLabels: {
            enabled: true,
            formatter: val => formatearUnidades(val),
            offsetX: 8,
            style: {
                fontSize: '12px',
                colors: ['#304758'],
                fontWeight: 'bold'
            }
        },
        xaxis: {
            labels: {
                formatter: val => formatearUnidades(val)
            }
        },
        yaxis: {
            labels: {
                style: {
                    fontSize: '10px',
                    fontWeight: '600',
                    colors: ['#333']
                },
                maxWidth: 250,
                show: true,
                align: 'right',
                minWidth: 0,
                offsetX: 0,
                offsetY: 0,
                rotate: 0
            }
        },
        tooltip: {
            y: { formatter: val => formatearUnidades(val) + ' vendidas' }
        },
        grid: {
            show: true,
            borderColor: '#e7e7e7',
            strokeDashArray: 0,
            position: 'back',
            xaxis: {
                lines: {
                    show: true
                }
            },
            yaxis: {
                lines: {
                    show: false
                }
            },
            row: {
                colors: undefined,
                opacity: 0.5
            },
            column: {
                colors: undefined,
                opacity: 0.5
            },
            padding: {
                top: 0,
                right: 0,
                bottom: 0,
                left: 0
            }
        }
    });

    ocultarIndicadorCarga('#tratamientoGrafica');
    chart.render();
     ('Gráfica de Modelos y Tratamientos renderizada con datos:', chartData);
}
// ===== FUNCIONES PARA CARGAR GRÁFICAS DE FACTURACIÓN =====
function cargarGraficasFacturacion() {
    const year = document.getElementById('year-select-facturacion').value;

    if (!year) {
        mostrarAlertaAdvertencia('Por favor, seleccione un año.');
        return;
    }

     ('Cargando gráficas de facturación para el año:', year);

    // Mostrar indicadores de carga
    mostrarIndicadorCarga('#estatus');
    mostrarIndicadorCarga('#tio');
    mostrarIndicadorCarga('#Sumaim');

    // Cargar datos de facturación general
    cargarDatosFacturacionGeneral(year);

    // Cargar datos de facturación detallada
    cargarDatosFacturacionDetallada(year);
}

function cargarDatosFacturacionGeneral(year) {
    $.get(`/cliente-api/get-facturacion-uam-anual-overview?year=${year}`, function (response) {
        // Cargar gráfica de facturación por estatus
        cargarGraficaFacturacionPorEstatus(response.sumaEstatus);

        // Actualizar total de facturación
        let totalFacturacion = 0;
        if (response.suma && Array.isArray(response.suma) && response.suma.length > 0) {
            totalFacturacion = parseFloat(response.suma[0].importe || 0);
        } else if (response.suma && typeof response.suma === 'object' && response.suma.importe) {
            totalFacturacion = parseFloat(response.suma.importe || 0);
        } else if (response.suma && !isNaN(parseFloat(response.suma))) {
            totalFacturacion = parseFloat(response.suma);
        }

        if (totalFacturacion > 0) {
            $('#suma-im').text(formatearPesos(totalFacturacion));
        } else {
            $('#suma-im').text('Sin información');
        }

    }).fail(function (error) {
        console.error('Error al cargar datos de facturación general:', error);
        mostrarAlertaError('Hubo un error al obtener los datos de facturación.');
        ocultarIndicadorCarga('#estatus');
    });
}

function cargarGraficaFacturacionPorEstatus(data) {
     ('Datos recibidos para facturación por estatus:', data);

    if (!data || data.length === 0) {
        document.querySelector("#estatus").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 450px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin información de facturación por estatus</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#estatus');
        return;
    }

    // Mapear estatus a nombres más claros
    function traducirEstatus(estatus) {
        const traducciones = {
            'paid': 'Pagado',
            'pending': 'Pendiente',
            'cancelled': 'Cancelado',
            'overdue': 'Vencido',
            'draft': 'Borrador',
            'pagado': 'Pagado',
            'pendiente': 'Pendiente',
            'cancelado': 'Cancelado',
            'vencido': 'Vencido',
            'borrador': 'Borrador'
        };
        return traducciones[estatus?.toLowerCase()] || estatus || 'Sin Estatus';
    }

    // Filtrar datos válidos con soporte para diferentes estructuras
    const datosValidos = data.filter(item => {
        const importe = parseFloat(item.importe || item.total || item.amount || 0);
        return !isNaN(importe) && importe > 0;
    }).map(item => ({
        estatus: item.estatus || item.status || item.estado || 'Sin Estatus',
        importe: parseFloat(item.importe || item.total || item.amount || 0)
    }));

     ('Datos válidos procesados para estatus:', datosValidos);

    if (datosValidos.length === 0) {
        document.querySelector("#estatus").innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 450px;">
                <div class="text-center">
                    <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Sin datos de facturación válidos</p>
                </div>
            </div>
        `;
        ocultarIndicadorCarga('#estatus');
        return;
    }

    const labels = datosValidos.map(item => traducirEstatus(item.estatus));
    const series = datosValidos.map(item => item.importe);

     ('Labels de estatus:', labels);
     ('Series de estatus:', series);

    const chart = new ApexCharts(document.querySelector("#estatus"), {
        series: [{ name: 'Importe Facturado', data: series }],
        chart: {
            type: 'bar',
            height: 500,
            toolbar: {
                show: true,
                tools: {
                    zoom: true,
                    zoomin: true,
                    zoomout: true,
                    pan: true,
                    reset: true
                }
            }
        },
        title: {
            text: 'Facturación por Estatus',
            align: 'center',
            style: { fontSize: '16px', fontWeight: 'bold' }
        },
        plotOptions: {
            bar: {
                horizontal: false,
                borderRadius: 6,
                dataLabels: { position: 'top' },
                columnWidth: '60%'
            }
        },
        dataLabels: {
            enabled: true,
            formatter: val => formatearPesosCorto(val),
            offsetY: -20,
            style: { fontSize: '12px', colors: ['#304758'], fontWeight: 'bold' }
        },
        xaxis: {
            categories: labels,
            labels: {
                style: { fontSize: '12px', fontWeight: 'bold' },
                rotate: -45
            }
        },
        yaxis: {
            labels: {
                formatter: val => formatearPesosCorto(val)
            }
        },
        tooltip: {
            y: {
                formatter: val => `${formatearPesos(val)} facturado`
            }
        },
        colors: ['#28a745', '#ffc107', '#dc3545', '#6c757d', '#17a2b8'],
        grid: {
            padding: {
                bottom: 20
            }
        }
    });

    ocultarIndicadorCarga('#estatus');
    chart.render();
}

    


    
function cargarDatosFacturacionDetallada(year) {
    const nombresMeses = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
                          'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'];

    $.get(`/cliente-api/get-facturacion-uam-anual-details?year=${year}`, function (response) {
        // Cargar gráfica de facturación mensual
        cargarGraficaFacturacionMensual(response.sumaMes, nombresMeses);

        // Cargar gráfica de facturación por tipo
        cargarGraficaFacturacionPorTipo(response.sumaClase);

    }).fail(function (error) {
        console.error('Error al cargar datos de facturación detallada:', error);
        mostrarAlertaError('Hubo un error al obtener los datos detallados de facturación.');
        ocultarIndicadorCarga('#Sumaim');
        ocultarIndicadorCarga('#tio');
    });
}

function cargarGraficaFacturacionMensual(data, nombresMeses) {
    const ordenados = data.sort((a, b) => parseInt(a.mes) - parseInt(b.mes));
    const labels = ordenados.map(item => nombresMeses[parseInt(item.mes) - 1] || 'Sin Mes');
    const series = ordenados.map(item => parseFloat(item.importe));

    const chart = new ApexCharts(document.querySelector("#Sumaim"), {
        series: [{
            name: 'Facturación Mensual',
            data: series
        }],
        chart: {
            type: 'bar',
            height: 450
        },
        title: { text: 'Facturación Mensual', align: 'center' },
        plotOptions: {
            bar: {
                borderRadius: 6,
                horizontal: false
            }
        },
        colors: ['#008FFB'],
        dataLabels: {
            enabled: true,
            formatter: val => formatearPesosCorto(val),
            style: { fontSize: '12px' }
        },
        xaxis: {
            categories: labels
        },
        yaxis: {
            labels: {
                formatter: val => formatearPesosCorto(val)
            }
        },
        tooltip: {
            y: {
                formatter: val => formatearPesos(val)
            }
        }
    });

    ocultarIndicadorCarga('#Sumaim');
    chart.render();
}

function cargarGraficaFacturacionPorTipo(data) {
    const labels = data.map(item => item.tipo);
    const series = data.map(item => parseFloat(item.importe));

    const chart = new ApexCharts(document.querySelector("#tio"), {
        series: series,
        chart: {
            type: 'donut',
            height: 450
        },
        labels: labels,
        title: { text: 'Facturación por Tipo', align: 'center' },
        plotOptions: {
            pie: {
                startAngle: -90,
                endAngle: 270
            }
        },
        dataLabels: {
            enabled: true,
            formatter: function (val, opts) {
                const value = opts.w.globals.series[opts.seriesIndex];
                return `${formatearPesosCorto(value)} (${val.toFixed(1)}%)`;
            }
        },
        fill: {
            type: 'gradient'
        },
        legend: {
            formatter: function (val, opts) {
                const value = opts.w.globals.series[opts.seriesIndex];
                return `${val}: ${formatearPesosCorto(value)}`;
            }
        },
        tooltip: {
            y: { formatter: val => formatearPesos(val) }
        },
        responsive: [{
            breakpoint: 480,
            options: {
                chart: { width: 250 },
                legend: { position: 'bottom' }
            }
        }]
    });

    ocultarIndicadorCarga('#tio');
    chart.render();
}

// ===== INICIALIZACIÓN AL CARGAR LA PÁGINA =====
$(document).ready(function() {
     ('Dashboard inicializando...');

    // Verificar que SweetAlert2 esté disponible
    verificarSweetAlert();

    // Configurar fecha por defecto (hoy)
    const hoy = new Date().toISOString().split('T')[0];
    document.getElementById('fecha-hoy').value = hoy;

    // Inicializar funcionalidades del Executive Dashboard
    initializeExecutiveDashboard();

    // Inicializar tendencias después de que se carguen los datos iniciales
    setTimeout(() => {
        updateTrendPercentages();
    }, 3000);

     ('Dashboard inicializado correctamente');
});

// ===== EXECUTIVE DASHBOARD FUNCTIONS =====

// Theme Toggle Functionality
function toggleTheme() {
    const dashboard = document.querySelector('.executive-dashboard');
    const html = document.documentElement;
    const body = document.body;
    const themeIcon = document.getElementById('theme-icon');
    const currentTheme = dashboard.getAttribute('data-theme');

    if (currentTheme === 'light') {
        dashboard.setAttribute('data-theme', 'dark');
        html.setAttribute('data-theme', 'dark');
        body.setAttribute('data-theme', 'dark');
        themeIcon.textContent = '☀️';
        localStorage.setItem('dashboard-theme', 'dark');
    } else {
        dashboard.setAttribute('data-theme', 'light');
        html.setAttribute('data-theme', 'light');
        body.setAttribute('data-theme', 'light');
        themeIcon.textContent = '🌙';
        localStorage.setItem('dashboard-theme', 'light');
    }

    // Force text color update after theme change
    setTimeout(forceDarkModeTextColors, 100);
}

// Initialize Executive Dashboard
function initializeExecutiveDashboard() {
    // Load saved theme
    const savedTheme = localStorage.getItem('dashboard-theme') || 'light';
    const dashboard = document.querySelector('.executive-dashboard');
    const html = document.documentElement;
    const body = document.body;
    const themeIcon = document.getElementById('theme-icon');

    dashboard.setAttribute('data-theme', savedTheme);
    html.setAttribute('data-theme', savedTheme);
    body.setAttribute('data-theme', savedTheme);
    themeIcon.textContent = savedTheme === 'dark' ? '☀️' : '🌙';

    // Update date and time
    updateDateTime();
    setInterval(updateDateTime, 1000);

    // Initialize sucursales summary
    updateSucursalesSummary();

    // Calculate efficiency metric
    updateEfficiencyMetric();

    // Add smooth animations to details and initialize open sections
    const detailsElements = document.querySelectorAll('details');
    detailsElements.forEach(details => {
        // Initialize icons for open sections
        const icon = details.querySelector('summary i:last-child');
        if (details.open && icon) {
            icon.style.transform = 'rotate(180deg)';
        }

        details.addEventListener('toggle', function() {
            const icon = this.querySelector('summary i:last-child');
            if (this.open) {
                icon.style.transform = 'rotate(180deg)';

                // Auto-load charts when section is opened
                if (this.closest('.detailed-analysis')) {
                    setTimeout(() => {
                        console.log('🔄 Auto-cargando gráficas de análisis detallado...');
                        cargarGraficasIngresosDiarios(true); // isAutoLoad = true
                    }, 300);
                }
            } else {
                icon.style.transform = 'rotate(0deg)';
            }
        });

        // Auto-load charts for sections that are open by default
        if (details.open && details.closest('.detailed-analysis')) {
            setTimeout(() => {
                console.log('🔄 Auto-cargando gráficas iniciales...');
                // Verificar si las sucursales ya están cargadas antes de cargar gráficas
                const sucursalesDisponibles = document.querySelectorAll('input[name="sucursal"]').length > 0;
                if (sucursalesDisponibles) {
                    cargarGraficasIngresosDiarios(true); // isAutoLoad = true
                } else {
                    console.log('⏳ Esperando a que se carguen las sucursales...');
                    // Esperar más tiempo para que se carguen las sucursales
                    setTimeout(() => {
                        cargarGraficasIngresosDiarios(true); // isAutoLoad = true
                    }, 3000);
                }
            }, 2000);
        }
    });

    // Force text colors after initialization
    setTimeout(forceDarkModeTextColors, 500);
}

// Update Date and Time
function updateDateTime() {
    const now = new Date();
    const dateElement = document.getElementById('current-date');
    const timeElement = document.getElementById('current-time');

    if (dateElement) {
        dateElement.textContent = now.toLocaleDateString('es-ES', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    if (timeElement) {
        timeElement.textContent = now.toLocaleTimeString('es-ES');
    }
}

// Toggle Sucursales Panel
function toggleSucursalesPanel() {
    const panel = document.getElementById('sucursales-panel');
    const icon = document.querySelector('#sucursales-summary i');

    if (panel.style.display === 'none' || !panel.style.display) {
        panel.style.display = 'block';
        icon.style.transform = 'rotate(180deg)';
    } else {
        panel.style.display = 'none';
        icon.style.transform = 'rotate(0deg)';
    }
}

// Update Sucursales Summary
function updateSucursalesSummary() {
    setTimeout(() => {
        const checkboxes = document.querySelectorAll('input[name="sucursal"]:checked');
        const total = document.querySelectorAll('input[name="sucursal"]').length;
        const selected = checkboxes.length;

        const summaryElement = document.getElementById('sucursales-count');
        if (summaryElement) {
            if (selected === total && total > 0) {
                summaryElement.textContent = `Todas las sucursales (${total})`;
            } else if (selected > 0) {
                summaryElement.textContent = `${selected} de ${total} sucursales`;
            } else {
                summaryElement.textContent = 'Ninguna sucursal seleccionada';
            }
        }
    }, 2000);
}

// Update Efficiency Metric
function updateEfficiencyMetric() {
    setTimeout(() => {
        const ventasElement = document.getElementById('ventas-total');
        const pagosElement = document.getElementById('pagos-total');
        const eficienciaElement = document.getElementById('eficiencia-total');

        if (ventasElement && pagosElement && eficienciaElement) {
            const ventas = parseFloat(ventasElement.textContent.replace(/[$,]/g, '')) || 0;
            const pagos = parseFloat(pagosElement.textContent.replace(/[$,]/g, '')) || 0;

            if (ventas > 0) {
                const eficiencia = Math.round((pagos / ventas) * 100);
                eficienciaElement.textContent = `${eficiencia}%`;

                // Update trend
                const trendElement = document.getElementById('eficiencia-trend');
                if (trendElement) {
                    if (eficiencia >= 90) {
                        trendElement.className = 'metric-trend positive';
                        trendElement.innerHTML = '<i class="fas fa-arrow-up"></i> Excelente';
                    } else if (eficiencia >= 70) {
                        trendElement.className = 'metric-trend neutral';
                        trendElement.innerHTML = '<i class="fas fa-minus"></i> Bueno';
                    } else {
                        trendElement.className = 'metric-trend negative';
                        trendElement.innerHTML = '<i class="fas fa-arrow-down"></i> Mejorar';
                    }
                }
            }
        }
    }, 3000);
}

// Calculate and update trend percentages
function updateTrendPercentages() {
    const todayDate = document.getElementById('fecha-hoy').value;
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')].map(cb => cb.value).join(',');

    if (!todayDate || !sucursales) {
        console.log('No se pueden calcular tendencias: faltan fecha o sucursales');
        return;
    }

    console.log('Calculando tendencias para:', { todayDate, sucursales });

    fetch(`/cliente-api/get-ingreso-comparison?todayDate=${todayDate}&sucursales=${sucursales}`)
        .then(response => response.json())
        .then(data => {
            console.log('Datos de comparación recibidos:', data);

            // Calcular tendencias
            const trends = calculateTrends(data.today, data.yesterday);

            // Actualizar elementos de tendencia
            updateTrendElement('ventas-trend', trends.ventas);
            updateTrendElement('pagos-trend', trends.pagos);
            updateTrendElement('deuda-trend', trends.porCobrar);

        })
        .catch(error => {
            console.error('Error al obtener datos de comparación:', error);
            // Mostrar tendencias neutras en caso de error
            updateTrendElement('ventas-trend', { percentage: 0, isPositive: null });
            updateTrendElement('pagos-trend', { percentage: 0, isPositive: null });
            updateTrendElement('deuda-trend', { percentage: 0, isPositive: null });
        });
}

// Calculate trend percentages
function calculateTrends(today, yesterday) {
    const calculatePercentage = (current, previous) => {
        if (previous === 0) {
            return current > 0 ? { percentage: 100, isPositive: true } : { percentage: 0, isPositive: null };
        }
        const percentage = ((current - previous) / previous) * 100;
        return {
            percentage: Math.abs(percentage),
            isPositive: percentage > 0 ? true : (percentage < 0 ? false : null)
        };
    };

    return {
        ventas: calculatePercentage(today.ventas, yesterday.ventas),
        pagos: calculatePercentage(today.pagos, yesterday.pagos),
        porCobrar: calculatePercentage(today.porCobrar, yesterday.porCobrar)
    };
}

// Update trend element with calculated data
function updateTrendElement(elementId, trend) {
    const element = document.getElementById(elementId);
    if (!element) return;

    const percentage = trend.percentage.toFixed(1);

    if (trend.isPositive === true) {
        element.className = 'metric-trend positive';
        element.innerHTML = `<i class="fas fa-arrow-up"></i> +${percentage}%`;
    } else if (trend.isPositive === false) {
        element.className = 'metric-trend negative';
        element.innerHTML = `<i class="fas fa-arrow-down"></i> -${percentage}%`;
    } else {
        element.className = 'metric-trend neutral';
        element.innerHTML = `<i class="fas fa-minus"></i> ${percentage}%`;
    }
}

// Override the original cargarResumenIngresos to update efficiency and trends
const originalCargarResumenIngresos = window.cargarResumenIngresos;
window.cargarResumenIngresos = function() {
    if (originalCargarResumenIngresos) {
        originalCargarResumenIngresos();
        setTimeout(updateEfficiencyMetric, 1000);
        setTimeout(updateSucursalesSummary, 500);
        setTimeout(updateTrendPercentages, 1500); // Calcular tendencias después de cargar datos
    }
};

// Create alias for buscarMarcasYModelos to use the correct year selector
window.buscarMarcasYModelos = function() {
    if (typeof cargarGraficasMarcasYModelos === 'function') {
        cargarGraficasMarcasYModelos();
    }
};

// Function to force dark mode text colors after content loads
function forceDarkModeTextColors() {
    const currentTheme = document.querySelector('.executive-dashboard').getAttribute('data-theme');

    if (currentTheme === 'dark') {
        // Force white text for all titles and labels
        const elementsToFix = [
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'label', 'span:not(.metric-value):not(.metric-label)',
            'p', '.chart-title'
        ];

        elementsToFix.forEach(selector => {
            const elements = document.querySelectorAll(`.executive-dashboard ${selector}`);
            elements.forEach(el => {
                if (!el.closest('.metric-card')) {
                    el.style.color = 'var(--text-primary)';
                }
            });
        });

        // Fix any ApexCharts text elements
        setTimeout(() => {
            const apexTexts = document.querySelectorAll('.apexcharts-text, .apexcharts-title-text, .apexcharts-subtitle-text');
            apexTexts.forEach(el => {
                el.style.fill = '#f8fafc';
            });
        }, 1000);
    }
}

// Function to load billing/invoice data
window.buscarDatosFacturacion = function() {
    const year = document.getElementById('year-select-facturacion').value || new Date().getFullYear();
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')].map(cb => cb.value).join(',');

    console.log('Cargando datos de facturación...', { year, sucursales });

    if (!sucursales) {
        mostrarAlertaAdvertencia('Por favor, seleccione al menos una sucursal.');
        return;
    }

    // Show loading indicators
    const estatusElement = document.getElementById('estatus');
    const tioElement = document.getElementById('tio');
    const sumaimElement = document.getElementById('Sumaim');

    if (estatusElement) {
        estatusElement.innerHTML = '<div style="text-align: center; padding: 2rem;"><i class="fas fa-spinner fa-spin fa-2x"></i><p>Cargando facturas por estado...</p></div>';
    }
    if (tioElement) {
        tioElement.innerHTML = '<div style="text-align: center; padding: 2rem;"><i class="fas fa-spinner fa-spin fa-2x"></i><p>Cargando total facturado...</p></div>';
    }
    if (sumaimElement) {
        sumaimElement.innerHTML = '<div style="text-align: center; padding: 2rem;"><i class="fas fa-spinner fa-spin fa-2x"></i><p>Cargando facturación mensual...</p></div>';
    }

    // Make API call
    fetch(`/cliente-api/get-facturacion-data?year=${year}&sucursales=${sucursales}`)
        .then(response => response.json())
        .then(data => {
            console.log('Datos de facturación recibidos:', data);

            // Load charts with real data
            if (data.facturasPorEstado && typeof cargarGraficaFacturasPorEstado === 'function') {
                cargarGraficaFacturasPorEstado(data.facturasPorEstado);
            }

            if (data.facturacionPorSucursal && typeof cargarGraficaTotalFacturado === 'function') {
                cargarGraficaTotalFacturado(data.facturacionPorSucursal);
            }

            if (data.facturacionMensual && typeof cargarGraficaFacturacionMensual === 'function') {
                cargarGraficaFacturacionMensual(data.facturacionMensual);
            }

            mostrarMensajeExito('Datos de facturación actualizados correctamente');
        })
        .catch(error => {
            console.error('Error al cargar datos de facturación:', error);
            mostrarAlertaError('Error al cargar los datos de facturación. Por favor, intente de nuevo.');

            // Show error messages in charts
            if (estatusElement) {
                estatusElement.innerHTML = '<div style="text-align: center; padding: 2rem; color: var(--danger-red);"><i class="fas fa-exclamation-triangle fa-2x"></i><p>Error al cargar datos</p></div>';
            }
            if (tioElement) {
                tioElement.innerHTML = '<div style="text-align: center; padding: 2rem; color: var(--danger-red);"><i class="fas fa-exclamation-triangle fa-2x"></i><p>Error al cargar datos</p></div>';
            }
            if (sumaimElement) {
                sumaimElement.innerHTML = '<div style="text-align: center; padding: 2rem; color: var(--danger-red);"><i class="fas fa-exclamation-triangle fa-2x"></i><p>Error al cargar datos</p></div>';
            }
        });
};

// Observer to watch for new content and apply dark mode colors
function initializeDarkModeObserver() {
    const currentTheme = document.querySelector('.executive-dashboard').getAttribute('data-theme');

    if (currentTheme === 'dark') {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Wait a bit for content to render, then apply colors
                    setTimeout(forceDarkModeTextColors, 200);
                }
            });
        });

        // Start observing
        observer.observe(document.querySelector('.executive-dashboard'), {
            childList: true,
            subtree: true
        });
    }
}

// Initialize observer after dashboard loads
setTimeout(initializeDarkModeObserver, 1000);

</script>
{% endblock %}
