<?php

namespace App\Controller\Api\AppOptimo\SuperAdmin;

use App\Enum\ErrorCodes\AppOptimo\EventsErrorCodes;
use App\Service\API\IngresosService;
use App\Service\ErrorResponseService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;


/**
 * @Route("/cliente-api", name="api_")
 */
class GraficasAdminController extends AbstractController
{
    private EntityManagerInterface $em;
    private IngresosService $ingresosService;
    private ErrorResponseService $errorResponseService;


    public function __construct(
        EntityManagerInterface $em,
        IngresosService $ingresosService,
        ErrorResponseService $errorResponseService
    ) {
        $this->em = $em;
        $this->ingresosService = $ingresosService;
        $this->errorResponseService = $errorResponseService;
    }


    /**
     *
     *
     * @Route("/get-ingreso-diario-overview", name="get-ingreso-diario-overview", methods={"GET"})
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @throws \Exception
     */
    public function getIngresoDiarioOverviewApi(Request $request): JsonResponse
    {
        $todayDate = $request->query->get("todayDate");
        $startDate = $request->query->get("startDate");
        $endDate = $request->query->get("endDate");
        $category = $request->query->get("category");
        $class  = $request->query->get("class");
        $sucursales  = $request->query->get("sucursales");


        if ( is_null($todayDate) ) {
            if ( is_null($startDate) || is_null($endDate )) {
                return $this->errorResponseService->createErrorResponse(EventsErrorCodes::EVENTS_INVALID_DATE);
            }
        }

        try {
            $sucursalesArray = array_filter(explode(',', $sucursales));
        } catch (\Exception $e) {
            return new JsonResponse(['code' => 'ids no generado'], 400);
        }

        $data = $this->ingresosService->getIngresoDiarioOverview($todayDate, $startDate, $endDate, $category, $class, $sucursalesArray);

        return new JsonResponse($data);
    }

    /**
     *
     *
     * @Route("/get-ingreso-diario-details", name="get-ingreso-diario-details", methods={"GET"})
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @throws \Exception
     */
    public function getIngresoDiarioDetailsApi(Request $request): JsonResponse
    {
        $todayDate = $request->query->get("todayDate");
        $startDate = $request->query->get("startDate");
        $endDate = $request->query->get("endDate");
        $category = $request->query->get("category");
        $class  = $request->query->get("class");
        $sucursales  = $request->query->get("sucursales");


        if ( is_null($todayDate) ) {
            if ( is_null($startDate) || is_null($endDate )) {
                return $this->errorResponseService->createErrorResponse(EventsErrorCodes::EVENTS_INVALID_DATE);
            }
        }

        try {
            $sucursalesArray = array_filter(explode(',', $sucursales));
        } catch (\Exception $e) {
            return new JsonResponse(['code' => 'ids no generado'], 400);
        }

        $data = $this->ingresosService->getIngresoDiarioDetails($todayDate, $startDate, $endDate, $category, $class, $sucursalesArray);

        return new JsonResponse($data);
    }

    /**
     * @Route("/get-facturacion-data", name="get-facturacion-data", methods={"GET"})
     */
    public function getFacturacionDataApi(Request $request): JsonResponse
    {
        $year = $request->query->get("year", date('Y'));
        $sucursales = $request->query->get("sucursales");

        try {
            $sucursalesArray = $sucursales ? array_filter(explode(',', $sucursales)) : [];
        } catch (\Exception $e) {
            return new JsonResponse(['code' => 'Error en sucursales'], 400);
        }

        $data = $this->ingresosService->getFacturacionData($year, $sucursalesArray);

        return new JsonResponse($data);
    }

    /**
     * @Route("/get-ingreso-comparison", name="get-ingreso-comparison", methods={"GET"})
     */
    public function getIngresoComparisonApi(Request $request): JsonResponse
    {
        $todayDate = $request->query->get("todayDate", date('Y-m-d'));
        $sucursales = $request->query->get("sucursales");

        try {
            $sucursalesArray = $sucursales ? array_filter(explode(',', $sucursales)) : [];
        } catch (\Exception $e) {
            return new JsonResponse(['code' => 'Error en sucursales'], 400);
        }

        $data = $this->ingresosService->getIngresoDiarioComparison($todayDate, $sucursalesArray);

        return new JsonResponse($data);
    }

    /**
     *
     *
     * @Route("/get-ingreso-anual-overview", name="get-ingreso-anual-overview", methods={"GET"})
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @throws \Exception
     */
    public function getIngersosAnualOverviewApi(Request $request): JsonResponse
    {
        $sucursales  = $request->query->get("sucursales");
        $year = $request->query->get("year");

        if ( is_null($year) ) {
            return $this->errorResponseService->createErrorResponse(EventsErrorCodes::EVENTS_INVALID_DATE);
        }

        try {
            $sucursalesArray = array_filter(explode(',', $sucursales));
        } catch (\Exception $e) {
            return new JsonResponse(['code' => 'ids no generado'], 400);
        }

        $data = $this->ingresosService->getIngresoAnual($year, $sucursalesArray);

        return new JsonResponse($data);
    }

    /**
     *
     *
     * @Route("/get-ingreso-anual-details", name="get-ingreso-anual-details", methods={"GET"})
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @throws \Exception
     */
    public function getMarcasAnualApi(Request $request)
    {
        $sucursales  = $request->query->get("sucursales");
        $year = $request->query->get("year");

        if ( is_null($year) ) {
            return $this->errorResponseService->createErrorResponse(EventsErrorCodes::EVENTS_INVALID_DATE);
        }

        try {
            $sucursalesArray = array_filter(explode(',', $sucursales));
        } catch (\Exception $e) {
            return new JsonResponse(['code' => 'ids no generado'], 400);
        }

        $marcas = $this->ingresosService->getMarcasAnual($year, $sucursalesArray);
        $dtm = $this->ingresosService->getDTMarcasAnual($year);

        return new JsonResponse([
            'marcas' => $marcas['marcas'],
            'dtm' => $dtm[0]
        ]);
    }

    /**
     *
     *
     * @Route("/get-modelos-marca-anual", name="get-modelos-marca-anual", methods={"GET"})
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @throws \Exception
     */
    public function getModelosMarcaAnualApi(Request $request)
    {
        $marca = $request->query->get("marca");
        $year = $request->query->get("year");


        if ( is_null($year) ) {
            return $this->errorResponseService->createErrorResponse(EventsErrorCodes::EVENTS_INVALID_DATE);
        }

        $data1 = $this->ingresosService->getModelosMarcasAnual($marca, $year);

        return new JsonResponse([
            "modelos" => $data1[0]
        ]);
    }


    /**
     *
     *
     * @Route("/get-dtm-anual", name="get-dtm-anual", methods={"GET"})
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @throws \Exception
     */
  public function getDTMarcasAnual(Request $request)
    {
        $year = $request->query->get("year");

        if ( is_null($year) ) {
            return $this->errorResponseService->createErrorResponse(EventsErrorCodes::EVENTS_INVALID_DATE);
        }

        $data = $this->ingresosService->getDTMarcasAnual($year);

        return new JsonResponse($data);
    }

    /**
     *
     * @Route("/get-facturacion-uam-anual-overview", name="get-facturacion-uam-anual-overview", methods={"GET"})
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @throws \Exception
     */
    public function getFacturacionUAMAnualOverviewApi(Request $request)
    {
        $year = $request->query->get("year");

        if (is_null($year)) {
            return $this->errorResponseService->createErrorResponse(EventsErrorCodes::EVENTS_INVALID_DATE);
        }

        $data = $this->ingresosService->getFacturacionUAMAnualOverview($year);

        return new JsonResponse($data);
    }

    /**
     *
     * @Route("/get-facturacion-uam-anual-details", name="get-facturacion-uam-anual-details", methods={"GET"})
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @throws \Exception
     */
    public function getFacturacionUAMAnualDetailsApi(Request $request)
    {
        $year = $request->query->get("year");

        if (is_null($year)) {
            return $this->errorResponseService->createErrorResponse(EventsErrorCodes::EVENTS_INVALID_DATE);
        }

        $data = $this->ingresosService->getFacturacionUAMAnualDetails($year);

        return new JsonResponse($data);
    }


}