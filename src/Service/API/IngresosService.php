<?php
//
//namespace App\Service\API;
//
//use Doctrine\ORM\EntityManagerInterface;
//use Symfony\Component\HttpFoundation\JsonResponse;
//
//class IngresosService
//{
//    private EntityManagerInterface $em;
//
//    public function __construct(EntityManagerInterface $em)
//    {
//        $this->em = $em;
//    }
//
//    public function getIngresoDiario(
//        $todayDate,
//        $startDate,
//        $endDate,
//        $category,
//        $class,
//        $sucursales
//    )
//    {
//        // Query 1: Datos por sucursal
///*        $qb1 = $this->em->createQueryBuilder()
//            ->select('su.nombre AS sucursal')
//            ->addSelect('SUM(v.pagado) AS totalVenta')
//            ->addSelect('SUM(v.deuda) AS porCobrar')
//            ->from('App\Entity\Stockventa', 'sv')
//            ->join('sv.stockIdstock', 's')
//            ->join('sv.ventaIdventa', 'v')
//            ->join('v.sucursalIdsucursal', 'su')
//            ->join('s.productoIdproducto', 'p')
//            ->join('p.categoriaIdcategoria', 'ca')
//            ->join('ca.claseIdclase', 'cl')
//            ->where('v.status = :status')
//            ->andWhere('su.status = :status')
//            ->andWhere('v.cotizacion = 0');*/
//
//        $qb1 = $this->em->createQueryBuilder()
//            ->select('su.nombre AS sucursal')
//            ->addSelect('SUM(v.pagado) AS totalPagado')
//            ->addSelect('SUM(v.deuda) AS totalDeuda')
//            ->from('App\Entity\Venta', 'v')
//            ->join('v.sucursalIdsucursal', 'su')
//            ->join('App\Entity\Stockventa', 'sv', 'WITH', 'sv.ventaIdventa = v.idventa')
//            ->join('sv.stockIdstock', 's')
//            ->join('s.productoIdproducto', 'p')
//            ->join('p.categoriaIdcategoria', 'ca')
//            ->join('ca.claseIdclase', 'cl')
//            ->where('v.status = :status')
//            ->andWhere('v.cotizacion = 0')
//            ->andWhere('su.status = :status');
//
//
//
//        // Query 2: Datos por tipo de pago
///*        $qb2 = $this->em->createQueryBuilder()
//            ->select('pat.name AS paymentType')
//            ->addSelect('SUM(pa.monto) AS cobrado')
//            ->from('App\Entity\Stockventa', 'sv')
//            ->join('sv.stockIdstock', 's')
//            ->join('sv.ventaIdventa', 'v')
//            ->join('v.sucursalIdsucursal', 'su')
//            ->join('s.productoIdproducto', 'p')
//            ->join('p.categoriaIdcategoria', 'ca')
//            ->join('ca.claseIdclase', 'cl')
//            ->join('App\Entity\Pago', 'pa', 'WITH', 'pa.ventaIdventa = v.idventa')
//            ->join('pa.paymenttypeIdpaymenttype', 'pat')
//            ->where('v.status = :status')
//            ->where('p.status = :status')
//            ->andWhere('su.status = :status');*/
//
//        $qb2 = $this->em->createQueryBuilder()
//            ->select('pat.name AS paymentType')
//            ->addSelect('SUM(pa.monto) AS cobrado')
//            ->from('App\Entity\Pago', 'pa')
//            ->join('pa.ventaIdventa', 'v')
//            ->join('v.sucursalIdsucursal', 'su')
//            ->join('App\Entity\Stockventa', 'sv', 'WITH', 'sv.ventaIdventa = v.idventa')
//            ->join('sv.stockIdstock', 's')
//            ->join('s.productoIdproducto', 'p')
//            ->join('p.categoriaIdcategoria', 'ca')
//            ->join('ca.claseIdclase', 'cl')
//            ->join('pa.paymenttypeIdpaymenttype', 'pat')
//            ->where('v.status = :status')
//            ->andWhere('v.cotizacion = 0')
//            ->andWhere('su.status = :status');
//
//
//        // Query 3: Sumas totales globales (pagado + deuda)
//        $qb3 = $this->em->createQueryBuilder()
//            ->select('SUM(v.pagadototal) AS totalVenta')
//            ->addSelect('SUM(v.deuda) AS porCobrar')
//            ->from('App\Entity\Stockventa', 'sv')
//            ->join('sv.ventaIdventa', 'v')
//            ->join('v.sucursalIdsucursal', 'su')
//            ->join('sv.stockIdstock', 's')
//            ->join('s.productoIdproducto', 'p')
//            ->join('p.categoriaIdcategoria', 'ca')
//            ->join('ca.claseIdclase', 'cl')
//            ->where('v.status = :status')
//            ->andWhere('su.status = :status');
//        $qb3 = $this->em->createQueryBuilder()
//            ->select('SUM(v.pagadototal) AS totalVenta')
//            ->addSelect('SUM(v.deuda) AS porCobrar')
//            ->from('App\Entity\Venta', 'v')
//            ->join('v.sucursalIdsucursal', 'su')
//            ->join('App\Entity\Stockventa', 'sv', 'WITH', 'sv.ventaIdventa = v.idventa')
//            ->join('sv.stockIdstock', 's')
//            ->join('s.productoIdproducto', 'p')
//            ->join('p.categoriaIdcategoria', 'ca')
//            ->join('ca.claseIdclase', 'cl')
//            ->where('v.status = :status')
//            ->andWhere('v.cotizacion = 0')
//            ->andWhere('su.status = :status');
//
//
//        // Query 4: Suma total sólo de pagado
//        $qb4 = $this->em->createQueryBuilder()
//            ->select('SUM(pa.monto) AS totalVenta')
//            ->from('App\Entity\Stockventa', 'sv')
//            ->join('sv.ventaIdventa', 'v')
//            ->join('v.sucursalIdsucursal', 'su')
//            ->join('sv.stockIdstock', 's')
//            ->join('s.productoIdproducto', 'p')
//            ->join('p.categoriaIdcategoria', 'ca')
//            ->join('ca.claseIdclase', 'cl')
//            ->join('App\Entity\Pago', 'pa', 'WITH', 'pa.ventaIdventa = v.idventa')
//            ->where('v.status = :status')
//            ->andWhere('su.status = :status');
//        $qb4 = $this->em->createQueryBuilder()
//            ->select('SUM(pa.monto) AS totalVenta')
//            ->from('App\Entity\Pago', 'pa')
//            ->join('pa.ventaIdventa', 'v')
//            ->join('App\Entity\Stockventa', 'sv', 'WITH', 'sv.ventaIdventa = v.idventa')
//            ->join('sv.stockIdstock', 's')
//            ->join('s.productoIdproducto', 'p')
//            ->join('p.categoriaIdcategoria', 'ca')
//            ->join('ca.claseIdclase', 'cl')
//            ->join('v.sucursalIdsucursal', 'su')
//            ->where('v.status = :status')
//            ->andWhere('su.status = :status');
//
//
//
//        // Aplica filtros dinámicos a los cuatro query builders
//        $queryBuilders = [$qb1, $qb2, $qb3, $qb4];
//
//        foreach ($queryBuilders as $qb) {
//
//            $qb->setParameter('status', 1);
//
//            if ($todayDate !== null) {
//                $qb->andWhere('v.fechaventa >= :todayDate')
//                    ->setParameter('todayDate', $todayDate);
//            }
//            if ($startDate !== null && $endDate !== null) {
//                $qb->andWhere('v.fechaventa BETWEEN :startDate AND :endDate')
//                    ->setParameter('startDate', $startDate)
//                    ->setParameter('endDate', $endDate);
//            }
//            if ($category !== null) {
//                $qb->andWhere('ca.nombre LIKE :category')
//                    ->setParameter('category', '%' . $category . '%');
//            }
//            if ($class !== null) {
//                $qb->andWhere('cl.nombre LIKE :class')
//                    ->setParameter('class', '%' . $class . '%');
//            }
//            if (count($sucursales) > 0) {
//                $qb->andWhere('su.idsucursal IN (:sucursales)')
//                    ->setParameter('sucursales', $sucursales);
//            }
//        }
//
//        // Agrupamientos
//        $qb1->groupBy('su.idsucursal');
//        $qb2->groupBy('pat.idpaymenttype');
//
//        // Ejecutar queries
//        $res1 = $qb1->getQuery()->getResult();
//        $res2 = $qb2->getQuery()->getResult();
//        $res3 = $qb3->getQuery()->getSingleResult();
//        $res4 = $qb4->getQuery()->getSingleResult();
//
//        return [
//            'dataPerSucursal' => $res1,
//            'dataPerPaymentType' => $res2,
//            'totalVentaCobrado' => $res3,
//            'totalVenta' => $res4,
//        ];
//    }
//
//    public function getIngresoAnual(
//        $year,
//        $sucursales
//    )
//    {
//
//        $qb = $this->em->createQueryBuilder()
//            ->from('App\Entity\Stockventa', 'sv')
//            ->select('SUM(pa.monto) AS cobrado')
//            ->join('sv.stockIdstock', 's')
//            ->join('sv.ventaIdventa', 'v')
//            ->join('v.sucursalIdsucursal', 'su')
//            ->join('App\Entity\Pago', 'pa', 'WITH', 'pa.ventaIdventa = v.idventa')
//            ->join('pa.paymenttypeIdpaymenttype', 'pat')
//            ->where('v.status = :status')
//            ->andWhere('pa.status = :status')
//            ->andWhere('su.status = :status')
//            ->andWhere('pa.fecha BETWEEN :start AND :end')
//            ->setParameter('start', new \DateTimeImmutable("$year-01-01"))
//            ->setParameter('end', new \DateTimeImmutable("$year-12-31"))
//            ->setParameter('status', 1);
//
//        $qb = $this->em->createQueryBuilder()
//            ->from('App\Entity\Pago', 'pa')
//            ->select('SUM(pa.monto) AS cobrado')
//            ->join('pa.ventaIdventa', 'v')
//            ->join('v.sucursalIdsucursal', 'su')
//            ->andWhere('pa.status = :status')
//            ->andWhere('YEAR(pa.fecha) = :year')
//            ->setParameter('year', $year)
//            ->setParameter('status', "1");
//
//        if (count($sucursales) > 0) {
//            $qb->andWhere('su.idsucursal IN (:sucursales)')
//                ->setParameter('sucursales', $sucursales);
//        }
//
//        $qbMes = clone $qb;
//        $qbMes->addSelect('MONTH(v.fechaventa) AS ventaMonth')
//                ->groupBy('ventaMonth')
//                ->orderBy('ventaMonth', 'ASC');
//        $meses = $qbMes->getQuery()->getResult();
//
//        $qbSucursal = clone $qb;
//        $qbSucursal->addSelect('su.nombre AS sucursal')
//                    ->groupBy('su.idsucursal')
//                    ->orderBy('su.nombre', 'ASC');
//        $sucursales = $qbSucursal->getQuery()->getResult();
//
//        return [
//            'dataPerMonth' => $meses,
//            'dataPerBranch' => $sucursales
//        ];
//    }
//
//    public function getUnidadesAnual(
//        $year,
//        $sucursales
//    )
//    {
//        $qb = $this->em->createQueryBuilder();
//
//    }
//}


namespace App\Service\API;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;

class IngresosService
{
    private EntityManagerInterface $em;

    public function __construct(EntityManagerInterface $em)
    {
        $this->em = $em;
    }

    public function getIngresoDiarioOverview(
        $todayDate,
        $startDate,
        $endDate,
        $category,
        $class,
        $sucursales
    )
    {
        // Query para obtener totales de ventas y deuda (usando Venta directamente)
        $qb3 = $this->em->createQueryBuilder()
            ->select('SUM(v.total) AS totalVenta')
            ->addSelect('SUM(v.deuda) AS porCobrar')
            ->from('App\Entity\Venta', 'v')
            ->join('v.sucursalIdsucursal', 'su')
            ->join('v.tipoventaIdtipoventa', 'tv')
            ->where('v.status = :status')
            ->andWhere('tv.idtipoventa = 1799'); // Solo ventas BIMBO

        // Query para obtener total de pagos (usando Venta directamente)
        $qb4 = $this->em->createQueryBuilder()
            ->select('SUM(pa.monto) AS totalCobrado')
            ->from('App\Entity\Pago', 'pa')
            ->join('pa.ventaIdventa', 'v')
            ->join('v.sucursalIdsucursal', 'su')
            ->join('v.tipoventaIdtipoventa', 'tv')
            ->where('pa.status = :status')
            ->andWhere('v.status = :status')
            ->andWhere('tv.idtipoventa = 1799'); // Solo pagos de ventas BIMBO
/*            ->select('SUM(pa.monto) AS totalCobrado')
            ->from('App\Entity\Pago', 'pa')
            ->join('pa.ventaIdventa', 'v')
            ->join('App\Entity\Stockventa', 'sv', 'WITH', 'sv.ventaIdventa = v.idventa')
            ->join('sv.stockIdstock', 's')
            ->join('s.productoIdproducto', 'p')
            ->join('p.categoriaIdcategoria', 'ca')
            ->join('ca.claseIdclase', 'cl')
            ->join('v.sucursalIdsucursal', 'su')
            ->where('v.status = :status')
            ->andWhere('v.cotizacion = 0')
            ->andWhere('su.status = :status')
            ->andWhere('pa.status = :status');*/

        $this->ingresoDiarioSetParameters(
            [$qb3, $qb4],
            $todayDate,
            $startDate,
            $endDate,
            $category,
            $class,
            $sucursales
        );

        $res3 = $qb3->getQuery()->getSingleResult();
        $res4 = $qb4->getQuery()->getSingleResult();

        return [
            'sumaVentaCobrar' => $res3,
            'sumaPagos' => $res4,
        ];
    }

    public function getIngresoDiarioDetails(
        $todayDate,
        $startDate,
        $endDate,
        $category,
        $class,
        $sucursales
    )
    {
        // Query para datos por sucursal (usando Venta directamente)
        $qb1 = $this->em->createQueryBuilder()
            ->select('su.nombre AS sucursal')
            ->addSelect('SUM(v.total) AS totalPagado')  // Cambié de v.pagado a v.total
            ->addSelect('SUM(v.deuda) AS totalDeuda')
            ->from('App\Entity\Venta', 'v')
            ->join('v.sucursalIdsucursal', 'su')
            ->join('v.tipoventaIdtipoventa', 'tv')
            ->where('v.status = :status')
            ->andWhere('tv.idtipoventa = 1799'); // Solo ventas BIMBO

        // Query para datos por tipo de pago (usando Venta directamente)
        $qb2 = $this->em->createQueryBuilder()
            ->select('pat.name AS paymentType')
            ->addSelect('SUM(pa.monto) AS cobrado')
            ->from('App\Entity\Pago', 'pa')
            ->join('pa.ventaIdventa', 'v')
            ->join('v.sucursalIdsucursal', 'su')
            ->join('v.tipoventaIdtipoventa', 'tv')
            ->join('pa.paymenttypeIdpaymenttype', 'pat')
            ->where('v.status = :status')
            ->andWhere('pa.status = :status')
            ->andWhere('tv.idtipoventa = 1799'); // Solo pagos de ventas BIMBO

        $this->ingresoDiarioSetParameters(
            [$qb1, $qb2],
            $todayDate,
            $startDate,
            $endDate,
            $category,
            $class,
            $sucursales
        );

        $qb1->groupBy('su.idsucursal');
        $qb2->groupBy('pat.idpaymenttype');

        $res1 = $qb1->getQuery()->getResult();
        $res2 = $qb2->getQuery()->getResult();

        return [
            'dataPerSucursal' => $res1,
            'dataPerPaymentType' => $res2,
        ];
    }


    private function ingresoDiarioSetParameters(
        $queryBuilders,
        $todayDate,
        $startDate,
        $endDate,
        $category,
        $class,
        $sucursales
    )
    {
        foreach ($queryBuilders as $qb) {
            $qb->setParameter('status', 1);

            // Filtro de fecha exacta (solo el día seleccionado)
            if ($todayDate !== null) {
                // Crear rango del día completo (00:00:00 a 23:59:59)
                $startOfDay = $todayDate . ' 00:00:00';
                $endOfDay = $todayDate . ' 23:59:59';
                $qb->andWhere('v.fechaventa BETWEEN :startOfDay AND :endOfDay')
                    ->setParameter('startOfDay', $startOfDay)
                    ->setParameter('endOfDay', $endOfDay);
            }

            // Filtro de rango de fechas (cuando se usa rango)
            if ($startDate !== null && $endDate !== null) {
                // Crear rango completo desde inicio del primer día hasta final del último día
                $startOfRange = $startDate . ' 00:00:00';
                $endOfRange = $endDate . ' 23:59:59';
                $qb->andWhere('v.fechaventa BETWEEN :startOfRange AND :endOfRange')
                    ->setParameter('startOfRange', $startOfRange)
                    ->setParameter('endOfRange', $endOfRange);
            }

            // Filtros de categoría y clase (solo si se usan con Stockventa)
            // Como ahora usamos Venta directamente, estos filtros se omiten
            // Si se necesitan, habría que hacer JOIN con Stockventa

            // Filtro de sucursales
            if (count($sucursales) > 0) {
                $qb->andWhere('su.idsucursal IN (:sucursales)')
                    ->setParameter('sucursales', $sucursales);
            }
        }

        return $queryBuilders;
    }



    public function getIngresoAnual($year, $sucursales)
    {
        // Query base simplificado - solo los JOINs necesarios
        $qb = $this->em->createQueryBuilder()
            ->from('App\Entity\Pago', 'pa')
            ->select('SUM(pa.monto) AS cobrado')
            ->join('pa.ventaIdventa', 'v')
            ->join('v.sucursalIdsucursal', 'su')
            ->join('v.tipoventaIdtipoventa', 'tv')
            ->where('pa.status = :status')
            ->andWhere('v.status = :status')
            ->andWhere('v.cotizacion = 0')
            ->andWhere('YEAR(pa.fecha) = :year')
            ->andWhere('tv.idtipoventa = 1799') // Solo ventas BIMBO
            ->setParameter('year', $year)
            ->setParameter('status', 1);

        if (count($sucursales) > 0) {
            $qb->andWhere('su.idsucursal IN (:sucursales)')
                ->setParameter('sucursales', $sucursales);
        }

        $qbMes = clone $qb;
        $qbMes->addSelect('MONTH(v.fechaventa) AS ventaMonth')
            ->groupBy('ventaMonth')
            ->orderBy('ventaMonth', 'ASC');
        $meses = $qbMes->getQuery()->getResult();

        $qbSucursal = clone $qb;
        $qbSucursal->addSelect('su.nombre AS sucursal')
            ->groupBy('su.idsucursal')
            ->orderBy('su.nombre', 'ASC');
        $sucursalData = $qbSucursal->getQuery()->getResult();

        return [
            'dataPerMonth' => $meses,
            'dataPerBranch' => $sucursalData
        ];
    }

    public function getMarcasAnual(
        $year,
        $sucursales
    )
    {
        $qb = $this->em->createQueryBuilder();

        $qb->select('m.nombre AS marca, COUNT(sv.idstockventa) AS vendidos')
            ->from('App\Entity\Stockventa', 'sv')
            ->join('sv.ventaIdventa', 'v')
            ->join('sv.stockIdstock', 's')
            ->join('v.sucursalIdsucursal', 'su')
            ->join('v.tipoventaIdtipoventa', 'tv')
            ->join('s.productoIdproducto', 'p')
            ->join('p.marcaIdmarca', 'm')
            ->join('App\Entity\Productostranspasoalmacen', 'pta', 'WITH', 'pta.stockIdstock = s')
            ->join('pta.transpasoalmacenIdtranspasoalmacen', 't')
            ->join('App\Entity\Ordensalida', 'os', 'WITH', 'os.transpasoalmacenIdtranspasoalmacen = t')
            ->where('YEAR(v.fechaventa) = :year')
            ->andWhere('os.aceptada = 1')
            ->andWhere('v.status = 1')
            ->andWhere('tv.idtipoventa = 1799')
            ->andWhere('m.nombre NOT LIKE :bolsa')
            ->andWhere('m.nombre NOT LIKE :opticlear')
            ->groupBy('m.nombre')
            ->orderBy('vendidos', 'DESC')
            ->setParameter('year', $year)
            ->setParameter('bolsa', 'BOLSA DE ENTREGA')
            ->setParameter('opticlear', 'OPTICLEAR');



        $marcas = $qb->getQuery()->getResult();

        return ['marcas' => $marcas];
    }

    public function getModelosMarcasAnual(
        $marca,
        $year
    )
    {
        $qb = $this->em->createQueryBuilder()
            ->select('p.modelo AS modelo, COUNT(sv.idstockventa) AS vendidos')
            ->from('App\Entity\Stockventa', 'sv')
            ->join('sv.ventaIdventa', 'v')
            ->join('sv.stockIdstock', 's')
            ->join('v.sucursalIdsucursal', 'su')
            ->join('v.tipoventaIdtipoventa', 'tv')
            ->join('s.productoIdproducto', 'p')
            ->join('p.marcaIdmarca', 'm')
            ->join('App\Entity\Productostranspasoalmacen', 'pta', 'WITH', 'pta.stockIdstock = s')
            ->join('pta.transpasoalmacenIdtranspasoalmacen', 't')
            ->join('App\Entity\Ordensalida', 'os', 'WITH', 'os.transpasoalmacenIdtranspasoalmacen = t')
            ->where('YEAR(v.fechaventa) = :year')
            ->andWhere('os.aceptada = 1')
            ->andWhere('v.status = 1')
            ->andWhere('tv.idtipoventa = 1799')
            ->andWhere('m.nombre NOT LIKE :bolsa')
            ->andWhere('m.nombre NOT LIKE :opticlear')
            ->andWhere('m.idmarca = :marca')
            ->groupBy('p.modelo')
            ->orderBy('vendidos', 'DESC')
            ->setParameter('year', $year)
            ->setParameter('marca', $marca)
            ->setParameter('bolsa', 'BOLSA DE ENTREGA')
            ->setParameter('opticlear', 'OPTICLEAR');


        $modelos = $qb->getQuery()->getResult();

        return [$modelos];
    }

    public function getDTMarcasAnual($year)
    {

/*        $modelos = ['bolsa', 'opticlear', 'ESTUCHE OPTIMO', 'MICROFIBRA'];
        $ventaStatus = 1;
        $osStatus = 1;

        $qb = $this->em->createQuery(
            'SELECT
                dl.nombre AS diseno,
                tra.nombre AS tratamiento,
                mat.nombre AS material,
                COUNT(sv.idstockventa) AS vendidos
            FROM App\Entity\Stockventaordenlaboratorio svlo
                JOIN svlo.stockventaIdstockventa sv
                JOIN sv.ventaIdventa v
                JOIN sv.stockIdstock s
                JOIN s.productoIdproducto p
                JOIN p.marcaIdmarca m
                JOIN App\Entity\Productostranspasoalmacen pta WITH pta.stockIdstock = s
                JOIN pta.transpasoalmacenIdtranspasoalmacen t
                JOIN App\Entity\Ordensalida os WITH os.transpasoalmacenIdtranspasoalmacen = t

                JOIN svlo.ordenlaboratorioIdordenlaboratorio ol
                LEFT JOIN ol.disenolenteIddisenolente dl
                LEFT JOIN ol.tratamientoIdtratamiento tra
                LEFT JOIN ol.materialIdmaterial mat

            WHERE YEAR(v.fechaventa) = :year
                AND v.status = :vstatus
                AND os.aceptada = :osstatus
                AND p.modelo NOT IN (:modelos)

            GROUP BY
                dl.nombre,
                tra.nombre,
                mat.nombre

            ORDER BY vendidos DESC'
                )->setParameters([
                    'year' => $year,
                    'vstatus' => $ventaStatus,
                    'osstatus' => $osStatus,
                    'modelos' => $modelos
                ]);*/

        $status = 1;
        $tipoProducto = 2;

        $qb = $this->em->createQuery(
            'SELECT 
                p.modelo AS modelo,
                COUNT(sv.idstockventa) AS vendidos
            FROM App\Entity\Stockventa sv
            JOIN sv.ventaIdventa v
            JOIN sv.stockIdstock s
            JOIN s.productoIdproducto p
            JOIN p.marcaIdmarca m
            JOIN v.tipoventaIdtipoventa tv
            WHERE YEAR(v.fechaventa) = :year
                AND v.status = :status
                AND p.tipoproducto = :tipoProducto
                AND tv.idtipoventa = 1799
                AND p.modelo NOT IN (:modelosExcluidos)
                AND (
                    p.modelo LIKE :monofocal OR
                    p.modelo LIKE :bifocalFlat OR
                    p.modelo LIKE :bifocalBlended OR
                    p.modelo LIKE :progresivo OR
                    p.modelo LIKE :cr39 OR
                    p.modelo LIKE :policarbonato OR
                    p.modelo LIKE :hiIndex OR
                    p.modelo LIKE :ultraHiIndex OR
                    p.modelo LIKE :thinLite OR
                    p.modelo LIKE :polarizado OR
                    p.modelo LIKE :crizal OR
                    p.modelo LIKE :ar OR
                    p.modelo LIKE :blue OR
                    p.modelo LIKE :fotocromatico OR
                    p.modelo LIKE :w
               )
            GROUP BY p.idproducto
            ORDER BY vendidos DESC'
        )->setParameters([
            'year' => $year,
            'status' => $status,
            'tipoProducto' => $tipoProducto,
            'modelosExcluidos' => ['BLUE UAM'],
            'monofocal' => '%MONOFOCAL%',
            'bifocalFlat' => '%BIFOCAL FLAT TOP%',
            'bifocalBlended' => '%BIFOCAL BLENDED%',
            'progresivo' => '%PROGRESIVO%',
            'cr39' => '%CR-39%',
            'policarbonato' => '%POLICARBONATO%',
            'hiIndex' => '%HI INDEX%',
            'ultraHiIndex' => '%ULTRA HI INDEX%',
            'thinLite' => '%THIN AND LITE%',
            'polarizado' => '%POLARIZADO%',
            'crizal' => '%CRIZAL SAPPHIRE%',
            'ar' => '% AR %',
            'blue' => '%BLUE%',
            'fotocromatico' => '%FOTOCROMÁTICO%',
            'w' => '%W%'
        ]);

        $datos = $qb->getResult();

        return [ $datos];
    }

    public function getFacturacionUAMAnualOverview($year)
    {
        $parameters = [
            'year' => $year,
            'status' => 1,
            'cotizacion' => 0,
            'idempresa' => 1,
            'idclases' => [2, 3]
        ];

        $idAuthstage = 12;

        // Base QB
        $qbBase = $this->em->createQueryBuilder()
            ->from('App\Entity\Venta', 'v')
            ->join('v.clienteIdcliente', 'c')
            ->join('v.sucursalIdsucursal', 's')
            ->join('s.empresaIdempresa', 'e')
            ->join('v.tipoventaIdtipoventa', 'tv')
            ->leftJoin('v.authstageIdauthstage', 'auths')
            ->join('App\Entity\Clase', 'clase', 'WITH', 'clase.empresaIdempresa = e.idempresa')
            ->where('YEAR(v.fechaventa) = :year')
            ->andWhere('v.status = :status')
            ->andWhere('v.cotizacion = :cotizacion')
            ->andWhere('e.idempresa = :idempresa')
            ->andWhere('clase.idclase IN (:idclases)')
            ->andWhere('tv.idtipoventa = 1799')
            ->setParameters($parameters);

        // suma
        $qbSuma = clone $qbBase;
        $qbSuma->select('SUM(v.pagadototal) AS importe');
        $suma = $qbSuma->getQuery()->getResult();

        // sumaEstatus
        $qbEstatus = clone $qbBase;
        $qbEstatus->select('SUM(v.pagadototal) AS importe')
            ->addSelect('COALESCE(auths.name, \'Sin asociar\') AS estatus')
            ->andWhere('auths.idauthstage = :idauthstage')
            ->groupBy('auths.idauthstage')
            ->orderBy('importe', 'DESC')
            ->setParameter('idauthstage', $idAuthstage);
        $sumaEstatus = $qbEstatus->getQuery()->getResult();

        return [
            'suma' => $suma,
            'sumaEstatus' => $sumaEstatus
        ];
    }

    public function getFacturacionUAMAnualDetails($year)
    {
        $parameters = [
            'year' => $year,
            'status' => 1,
            'cotizacion' => 0,
            'idempresa' => 1,
            'idclases' => [2, 3],

        ];

        $idAuthstage = 12;

        // sumaClase
        $qbClase = $this->em->createQueryBuilder()
            ->select('SUM(v.pagadototal) AS importe')
            ->addSelect('cl.nombre AS tipo')
            ->from('App\Entity\Stockventa', 'sv')
            ->join('sv.ventaIdventa', 'v')
            ->join('v.authstageIdauthstage', 'ats')
            ->join('v.sucursalIdsucursal', 'su')
            ->join('su.empresaIdempresa', 'e')
            ->join('v.tipoventaIdtipoventa', 'tv')
            ->join('sv.stockIdstock', 's')
            ->join('s.productoIdproducto', 'p')
            ->join('p.categoriaIdcategoria', 'ca')
            ->join('ca.claseIdclase', 'cl')
            ->where('YEAR(v.fechaventa) = :year')
            ->andWhere('e.idempresa = :idempresa')
            ->andWhere('v.status = :status')
            ->andWhere('cl.idclase IN (:idclases)')
            ->andWhere('ats.idauthstage = :idauthstage')
            ->andWhere('v.cotizacion = :cotizacion')
            ->andWhere('tv.idtipoventa = 1799')
            ->groupBy('cl.idclase')
            ->setParameters($parameters)
            ->setParameter('idauthstage', $idAuthstage);
        $sumaClase = $qbClase->getQuery()->getResult();

        // sumaMes
        $qbMes = $this->em->createQueryBuilder()
            ->select('SUM(v.pagadototal) AS importe')
            ->addSelect('MONTH(v.fechaventa) AS mes')
            ->from('App\Entity\Venta', 'v')
            ->join('v.clienteIdcliente', 'c')
            ->join('v.sucursalIdsucursal', 's')
            ->join('s.empresaIdempresa', 'e')
            ->join('v.tipoventaIdtipoventa', 'tv')
            ->leftJoin('v.authstageIdauthstage', 'auths')
            ->join('App\Entity\Clase', 'clase', 'WITH', 'clase.empresaIdempresa = e.idempresa')
            ->where('YEAR(v.fechaventa) = :year')
            ->andWhere('v.status = :status')
            ->andWhere('v.cotizacion = :cotizacion')
            ->andWhere('e.idempresa = :idempresa')
            ->andWhere('clase.idclase IN (:idclases)')
            ->andWhere('tv.idtipoventa = 1799')
            ->groupBy('mes')
            ->orderBy('mes', 'ASC')
            ->setParameters($parameters);
        $sumaMes = $qbMes->getQuery()->getResult();

        return [
            'sumaClase' => $sumaClase,
            'sumaMes' => $sumaMes
        ];
    }

    /**
     * Get billing/invoice data for dashboard
     */
    public function getFacturacionData($year, $sucursales)
    {
        // Query para facturas por estado (usando SQL nativo)
        $sql1 = "SELECT vf.estado AS estado,
                        COUNT(vf.idventaFactura) AS cantidad,
                        SUM(vf.montoVenta) AS total
                 FROM ventaFactura vf
                 INNER JOIN venta v ON vf.venta_idventa = v.idventa
                 INNER JOIN sucursal su ON v.sucursal_idsucursal = su.idsucursal
                 INNER JOIN tipoVenta tv ON v.tipoventa_idtipoventa = tv.idtipoVenta
                 WHERE vf.status = :status
                 AND v.status = :status
                 AND tv.idtipoVenta = 1799
                 AND YEAR(vf.creacion) = :year";

        if (count($sucursales) > 0) {
            $sql1 .= " AND su.idsucursal IN (" . implode(',', array_map('intval', $sucursales)) . ")";
        }

        $sql1 .= " GROUP BY vf.estado";

        $stmt1 = $this->em->getConnection()->prepare($sql1);
        $stmt1->bindValue('status', 1);
        $stmt1->bindValue('year', $year);

        // Query para facturación mensual (usando SQL nativo para funciones de fecha)
        $sql2 = "SELECT MONTH(vf.creacion) AS mes,
                        SUM(vf.montoVenta) AS totalFacturado,
                        COUNT(vf.idventaFactura) AS cantidadFacturas
                 FROM ventaFactura vf
                 INNER JOIN venta v ON vf.venta_idventa = v.idventa
                 INNER JOIN sucursal su ON v.sucursal_idsucursal = su.idsucursal
                 INNER JOIN tipoVenta tv ON v.tipoventa_idtipoventa = tv.idtipoVenta
                 WHERE vf.status = :status
                 AND v.status = :status
                 AND tv.idtipoVenta = 1799
                 AND YEAR(vf.creacion) = :year";

        if (count($sucursales) > 0) {
            $sql2 .= " AND su.idsucursal IN (" . implode(',', array_map('intval', $sucursales)) . ")";
        }

        $sql2 .= " GROUP BY MONTH(vf.creacion) ORDER BY mes ASC";

        $stmt2 = $this->em->getConnection()->prepare($sql2);
        $stmt2->bindValue('status', 1);
        $stmt2->bindValue('year', $year);

        // Query para total facturado por sucursal (usando SQL nativo)
        $sql3 = "SELECT su.nombre AS sucursal,
                        SUM(vf.montoVenta) AS totalFacturado,
                        COUNT(vf.idventaFactura) AS cantidadFacturas
                 FROM ventaFactura vf
                 INNER JOIN venta v ON vf.venta_idventa = v.idventa
                 INNER JOIN sucursal su ON v.sucursal_idsucursal = su.idsucursal
                 INNER JOIN tipoVenta tv ON v.tipoventa_idtipoventa = tv.idtipoVenta
                 WHERE vf.status = :status
                 AND v.status = :status
                 AND tv.idtipoVenta = 1799
                 AND YEAR(vf.creacion) = :year";

        if (count($sucursales) > 0) {
            $sql3 .= " AND su.idsucursal IN (" . implode(',', array_map('intval', $sucursales)) . ")";
        }

        $sql3 .= " GROUP BY su.idsucursal, su.nombre ORDER BY totalFacturado DESC";

        $stmt3 = $this->em->getConnection()->prepare($sql3);
        $stmt3->bindValue('status', 1);
        $stmt3->bindValue('year', $year);

        // Ejecutar las consultas
        $facturasPorEstado = $stmt1->executeQuery()->fetchAllAssociative();
        $facturacionMensual = $stmt2->executeQuery()->fetchAllAssociative();
        $facturacionPorSucursal = $stmt3->executeQuery()->fetchAllAssociative();

        return [
            'facturasPorEstado' => $facturasPorEstado,
            'facturacionMensual' => $facturacionMensual,
            'facturacionPorSucursal' => $facturacionPorSucursal,
        ];
    }

    /**
     * Get comparison data for trend calculations
     */
    public function getIngresoDiarioComparison($todayDate, $sucursales)
    {
        // Calcular fecha del día anterior
        $yesterdayDate = date('Y-m-d', strtotime($todayDate . ' -1 day'));

        // Query para datos de hoy
        $qbToday = $this->em->createQueryBuilder()
            ->select('SUM(v.total) AS totalVenta')
            ->addSelect('SUM(v.deuda) AS porCobrar')
            ->from('App\Entity\Venta', 'v')
            ->join('v.sucursalIdsucursal', 'su')
            ->join('v.tipoventaIdtipoventa', 'tv')
            ->where('v.status = :status')
            ->andWhere('tv.idtipoventa = 1799')
            ->setParameter('status', 1);

        // Query para pagos de hoy
        $qbPagosToday = $this->em->createQueryBuilder()
            ->select('SUM(pa.monto) AS totalCobrado')
            ->from('App\Entity\Pago', 'pa')
            ->join('pa.ventaIdventa', 'v')
            ->join('v.sucursalIdsucursal', 'su')
            ->join('v.tipoventaIdtipoventa', 'tv')
            ->where('pa.status = :status')
            ->andWhere('v.status = :status')
            ->andWhere('tv.idtipoventa = 1799')
            ->setParameter('status', 1);

        // Query para datos de ayer
        $qbYesterday = $this->em->createQueryBuilder()
            ->select('SUM(v.total) AS totalVenta')
            ->addSelect('SUM(v.deuda) AS porCobrar')
            ->from('App\Entity\Venta', 'v')
            ->join('v.sucursalIdsucursal', 'su')
            ->join('v.tipoventaIdtipoventa', 'tv')
            ->where('v.status = :status')
            ->andWhere('tv.idtipoventa = 1799')
            ->setParameter('status', 1);

        // Query para pagos de ayer
        $qbPagosYesterday = $this->em->createQueryBuilder()
            ->select('SUM(pa.monto) AS totalCobrado')
            ->from('App\Entity\Pago', 'pa')
            ->join('pa.ventaIdventa', 'v')
            ->join('v.sucursalIdsucursal', 'su')
            ->join('v.tipoventaIdtipoventa', 'tv')
            ->where('pa.status = :status')
            ->andWhere('v.status = :status')
            ->andWhere('tv.idtipoventa = 1799')
            ->setParameter('status', 1);

        // Aplicar filtros de fecha
        $startOfToday = $todayDate . ' 00:00:00';
        $endOfToday = $todayDate . ' 23:59:59';
        $startOfYesterday = $yesterdayDate . ' 00:00:00';
        $endOfYesterday = $yesterdayDate . ' 23:59:59';

        $qbToday->andWhere('v.fechaventa BETWEEN :startOfToday AND :endOfToday')
                ->setParameter('startOfToday', $startOfToday)
                ->setParameter('endOfToday', $endOfToday);

        $qbPagosToday->andWhere('v.fechaventa BETWEEN :startOfToday AND :endOfToday')
                     ->setParameter('startOfToday', $startOfToday)
                     ->setParameter('endOfToday', $endOfToday);

        $qbYesterday->andWhere('v.fechaventa BETWEEN :startOfYesterday AND :endOfYesterday')
                    ->setParameter('startOfYesterday', $startOfYesterday)
                    ->setParameter('endOfYesterday', $endOfYesterday);

        $qbPagosYesterday->andWhere('v.fechaventa BETWEEN :startOfYesterday AND :endOfYesterday')
                         ->setParameter('startOfYesterday', $startOfYesterday)
                         ->setParameter('endOfYesterday', $endOfYesterday);

        // Aplicar filtro de sucursales si se especifica
        if (count($sucursales) > 0) {
            $qbToday->andWhere('su.idsucursal IN (:sucursales)')
                    ->setParameter('sucursales', $sucursales);
            $qbPagosToday->andWhere('su.idsucursal IN (:sucursales)')
                         ->setParameter('sucursales', $sucursales);
            $qbYesterday->andWhere('su.idsucursal IN (:sucursales)')
                        ->setParameter('sucursales', $sucursales);
            $qbPagosYesterday->andWhere('su.idsucursal IN (:sucursales)')
                             ->setParameter('sucursales', $sucursales);
        }

        $todayData = $qbToday->getQuery()->getOneOrNullResult();
        $todayPagos = $qbPagosToday->getQuery()->getOneOrNullResult();
        $yesterdayData = $qbYesterday->getQuery()->getOneOrNullResult();
        $yesterdayPagos = $qbPagosYesterday->getQuery()->getOneOrNullResult();

        return [
            'today' => [
                'ventas' => $todayData['totalVenta'] ?? 0,
                'pagos' => $todayPagos['totalCobrado'] ?? 0,
                'porCobrar' => $todayData['porCobrar'] ?? 0,
            ],
            'yesterday' => [
                'ventas' => $yesterdayData['totalVenta'] ?? 0,
                'pagos' => $yesterdayPagos['totalCobrado'] ?? 0,
                'porCobrar' => $yesterdayData['porCobrar'] ?? 0,
            ]
        ];
    }

}
