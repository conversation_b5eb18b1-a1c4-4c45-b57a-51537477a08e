-- Fix for the nombre_optometrista foreign key constraint error
-- This script will modify the column to be compatible with the foreign key

-- Step 1: First, check if there's existing data and back it up if needed
-- You may want to create a backup column first to preserve any existing names
ALTER TABLE `grupooptimo_pv360_v13`.`ordenLaboratorio` 
ADD COLUMN `nombre_optometrista_backup` VARCHAR(255) NULL;

-- Copy existing data to backup column (if any)
UPDATE `grupooptimo_pv360_v13`.`ordenLaboratorio` 
SET `nombre_optometrista_backup` = `nombre_optometrista`
WHERE `nombre_optometrista` IS NOT NULL;

-- Step 2: Drop the existing column and recreate it with the correct data type
ALTER TABLE `grupooptimo_pv360_v13`.`ordenLaboratorio` 
DROP COLUMN `nombre_optometrista`;

-- Step 3: Add the column with the correct data type (unsigned integer to match usuario.idusuario)
ALTER TABLE `grupooptimo_pv360_v13`.`ordenLaboratorio` 
ADD COLUMN `nombre_optometrista` INT UNSIGNED NULL;

-- Step 4: Now create the foreign key constraint
ALTER TABLE `grupooptimo_pv360_v13`.`ordenLaboratorio` 
ADD CONSTRAINT `fk_ordenLaboratorio_usuario1`
  FOREIGN KEY (`nombre_optometrista`)
  REFERENCES `grupooptimo_pv360_v13`.`usuario` (`idusuario`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

-- Step 5: Create an index for better performance
CREATE INDEX `idx_ordenLaboratorio_nombre_optometrista` 
ON `grupooptimo_pv360_v13`.`ordenLaboratorio` (`nombre_optometrista`);

-- Optional: If you need to migrate existing data, you could try to match names to user IDs
-- This would require a more complex query based on your specific data
-- Example (uncomment and modify as needed):
-- UPDATE `grupooptimo_pv360_v13`.`ordenLaboratorio` ol
-- JOIN `grupooptimo_pv360_v13`.`usuario` u ON 
--   CONCAT(u.nombre, ' ', u.apellidoPaterno, ' ', IFNULL(u.apellidoMaterno, '')) = ol.nombre_optometrista_backup
-- SET ol.nombre_optometrista = u.idusuario
-- WHERE ol.nombre_optometrista_backup IS NOT NULL;
